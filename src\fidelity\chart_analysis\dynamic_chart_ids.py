#!/usr/bin/env python3
"""
Dynamic Chart ID Generator for StockCharts Integration

This module handles the dynamic generation of StockCharts chart IDs using the scraper,
with different configurations for daily and weekly charts.
"""

import asyncio
import os
import sys
import time
from typing import Optional, Dict, Tuple
from dataclasses import dataclass

# Add the sc-scrapper directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
scrapper_dir = os.path.join(current_dir, 'sc-scrapper')
sys.path.insert(0, scrapper_dir)

try:
    from get_sc_id import get_chart_id, Settings
except ImportError as e:
    print(f"Warning: Could not import scraper module: {e}")
    print("Make sure the sc-scrapper directory contains get_sc_id.py")
    get_chart_id = None
    Settings = None

@dataclass
class ChartConfig:
    """Configuration for chart generation"""
    chart_type: str  # 'daily' or 'weekly'
    ema: str = "20"
    sma1: str = "50"
    sma2: str = "200"
    symbol: str = "AAPL"  # Default symbol for ID generation

# Chart configurations
DAILY_CONFIG = ChartConfig(
    chart_type='daily',
    ema="20",
    sma1="50",
    sma2="200"
)

WEEKLY_CONFIG = ChartConfig(
    chart_type='weekly',
    ema="0",
    sma1="10",  # As requested: SMA 10,30 for weekly
    sma2="30"
)

class ChartIDManager:
    """Manages dynamic chart ID generation and caching"""

    def __init__(self):
        self.daily_id: Optional[str] = None
        self.weekly_id: Optional[str] = None
        self.last_generated: Dict[str, float] = {}
        self.cache_duration = 3600  # Cache for 1 hour

    def _is_cache_valid(self, chart_type: str) -> bool:
        """Check if cached ID is still valid"""
        if chart_type not in self.last_generated:
            return False
        return (time.time() - self.last_generated[chart_type]) < self.cache_duration

    async def get_daily_chart_id(self, force_refresh: bool = False) -> Optional[str]:
        """Get daily chart ID, using cache if valid"""
        if not force_refresh and self.daily_id and self._is_cache_valid('daily'):
            print(f"Using cached daily chart ID: {self.daily_id}")
            return self.daily_id

        print("Generating new daily chart ID...")
        self.daily_id = await self._generate_chart_id(DAILY_CONFIG)
        if self.daily_id:
            self.last_generated['daily'] = time.time()
            print(f"✅ Generated daily chart ID: {self.daily_id}")
        return self.daily_id

    async def get_weekly_chart_id(self, force_refresh: bool = False) -> Optional[str]:
        """Get weekly chart ID with SMA 10,20, using cache if valid"""
        if not force_refresh and self.weekly_id and self._is_cache_valid('weekly'):
            print(f"Using cached weekly chart ID: {self.weekly_id}")
            return self.weekly_id

        print("Generating new weekly chart ID with SMA 10,20...")
        self.weekly_id = await self._generate_chart_id(WEEKLY_CONFIG)
        if self.weekly_id:
            self.last_generated['weekly'] = time.time()
            print(f"✅ Generated weekly chart ID: {self.weekly_id}")
        return self.weekly_id

    async def _generate_chart_id(self, config: ChartConfig) -> Optional[str]:
        """Generate chart ID using the scraper"""
        if not get_chart_id or not Settings:
            print("❌ Scraper module not available")
            return None

        try:
            settings = Settings(
                symbol=config.symbol,
                ema=config.ema,
                sma1=config.sma1,
                sma2=config.sma2,
                headless=True,
                slow_mo=0
            )

            print(f"Generating {config.chart_type} chart with settings:")
            print(f"  Symbol: {settings.symbol}")
            print(f"  EMA: {settings.ema}")
            print(f"  SMA1: {settings.sma1}")
            print(f"  SMA2: {settings.sma2}")

            chart_id = await get_chart_id(settings)
            return chart_id

        except Exception as e:
            print(f"❌ Error generating {config.chart_type} chart ID: {e}")
            return None

    async def get_both_chart_ids(self, force_refresh: bool = False) -> Tuple[Optional[str], Optional[str]]:
        """Get both daily and weekly chart IDs"""
        print("🎯 Generating chart IDs for StockCharts integration...")

        # Generate both IDs concurrently for efficiency
        daily_task = self.get_daily_chart_id(force_refresh)
        weekly_task = self.get_weekly_chart_id(force_refresh)

        daily_id, weekly_id = await asyncio.gather(daily_task, weekly_task, return_exceptions=True)

        # Handle any exceptions
        if isinstance(daily_id, Exception):
            print(f"❌ Daily chart ID generation failed: {daily_id}")
            daily_id = None

        if isinstance(weekly_id, Exception):
            print(f"❌ Weekly chart ID generation failed: {weekly_id}")
            weekly_id = None

        print(f"\n📊 Chart ID Generation Results:")
        print(f"  Daily ID:  {daily_id or 'Failed'}")
        print(f"  Weekly ID: {weekly_id or 'Failed'}")

        return daily_id, weekly_id

    def get_cached_ids(self) -> Tuple[Optional[str], Optional[str]]:
        """Get currently cached IDs without regeneration"""
        return self.daily_id, self.weekly_id

# Global instance
chart_id_manager = ChartIDManager()

async def initialize_chart_ids(force_refresh: bool = False) -> Tuple[Optional[str], Optional[str]]:
    """Initialize chart IDs for the web UI"""
    return await chart_id_manager.get_both_chart_ids(force_refresh)

def get_current_chart_ids() -> Tuple[Optional[str], Optional[str]]:
    """Get current chart IDs (cached)"""
    return chart_id_manager.get_cached_ids()

async def refresh_chart_ids() -> Tuple[Optional[str], Optional[str]]:
    """Force refresh of chart IDs"""
    return await chart_id_manager.get_both_chart_ids(force_refresh=True)

if __name__ == "__main__":
    """Test the chart ID generation"""
    async def test():
        print("🧪 Testing Chart ID Generation")
        print("=" * 50)

        daily_id, weekly_id = await initialize_chart_ids()

        print(f"\n✅ Test Results:")
        print(f"Daily Chart ID:  {daily_id}")
        print(f"Weekly Chart ID: {weekly_id}")

        if daily_id and weekly_id:
            print("\n🎉 Both chart IDs generated successfully!")
            print(f"Daily URL template:  https://stockcharts.com/c-sc/sc?s=SYMBOL&p=D&yr=0&mn=4&dy=0&i={daily_id}&r=tdy")
            print(f"Weekly URL template: https://stockcharts.com/c-sc/sc?s=SYMBOL&p=W&yr=2&mn=6&dy=0&i={weekly_id}&r=tdy")
        else:
            print("\n❌ Chart ID generation failed")

    asyncio.run(test())
