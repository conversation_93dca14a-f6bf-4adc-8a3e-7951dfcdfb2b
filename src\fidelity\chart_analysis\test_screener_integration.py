#!/usr/bin/env python3
"""
Test script for ETF Analytics integration with screener
"""

import sys
import os

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(src_dir)

from etf_analytics import etf_analytics

def test_screener_integration():
    print("🧪 Testing ETF Analytics Screener Integration")
    print("=" * 60)
    
    # Simulate some screener stocks (mix of stocks with and without theme data)
    test_screener_stocks = [
        'TSLA', 'AAPL', 'MSFT', 'GOOGL', 'AMZN',  # Big tech
        'OKLO', 'ACHR', 'PL', 'ASTS',  # Space/Nuclear
        'NVDA', 'AMD', 'INTC',  # Semiconductors
        'XYZ123', 'FAKE456'  # Non-existent stocks
    ]
    
    print(f"\n📊 Test Screener Stocks ({len(test_screener_stocks)}):")
    print(f"   {', '.join(test_screener_stocks)}")
    
    # Test analytics with screener filter
    print("\n🎯 Analytics Summary (Screener-Filtered):")
    summary = etf_analytics.get_analytics_summary(test_screener_stocks)
    for key, value in summary.items():
        print(f"   {key}: {value}")
    
    # Test available themes
    print("\n🏷️ Available Themes for Screener:")
    themes = etf_analytics.get_available_themes(test_screener_stocks)
    print(f"   Found {len(themes)} themes: {themes[:5]}{'...' if len(themes) > 5 else ''}")
    
    # Test theme filtering
    if themes:
        test_theme = themes[0]
        print(f"\n🔍 Stocks in '{test_theme}' theme:")
        theme_stocks = etf_analytics.get_stocks_by_theme(test_theme, test_screener_stocks)
        print(f"   {theme_stocks}")
    
    # Test pie chart data
    print("\n🥧 Pie Chart Data (Top 5):")
    labels, values, scores = etf_analytics.get_pie_chart_data(test_screener_stocks)
    for i in range(min(5, len(labels))):
        print(f"   {labels[i]}: {values[i]} stocks, Score {scores[i]:.1f}")
    
    # Test comparison: All ETF stocks vs Screener stocks
    print("\n📈 Comparison: All ETF vs Screener")
    all_summary = etf_analytics.get_analytics_summary()
    screener_summary = etf_analytics.get_analytics_summary(test_screener_stocks)
    
    print(f"   All ETF stocks: {all_summary['total_stocks']} stocks, {all_summary['total_niches']} themes")
    print(f"   Screener stocks: {screener_summary['total_stocks']} stocks, {screener_summary['total_niches']} themes")
    print(f"   Coverage: {screener_summary['total_stocks']}/{len(test_screener_stocks)} = {screener_summary['total_stocks']/len(test_screener_stocks)*100:.1f}%")
    
    print("\n✅ Screener integration test completed!")

if __name__ == "__main__":
    test_screener_integration()
