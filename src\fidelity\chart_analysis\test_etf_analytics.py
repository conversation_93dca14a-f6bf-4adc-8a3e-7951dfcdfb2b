#!/usr/bin/env python3
"""
Test script for ETF Analytics integration
"""

import sys
import os

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(src_dir)

from etf_analytics import etf_analytics

def test_analytics():
    print("🧪 Testing ETF Analytics Integration")
    print("=" * 50)
    
    # Test analytics summary
    print("\n📊 Analytics Summary:")
    summary = etf_analytics.get_analytics_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")
    
    # Test pie chart data
    print("\n🥧 Pie Chart Data:")
    labels, values, scores = etf_analytics.get_pie_chart_data()
    print(f"   Labels: {labels[:5]}{'...' if len(labels) > 5 else ''}")
    print(f"   Values: {values[:5]}{'...' if len(values) > 5 else ''}")
    print(f"   Scores: {[f'{s:.1f}' for s in scores[:5]]}{'...' if len(scores) > 5 else ''}")
    
    # Test top niches
    print("\n🏆 Top 5 Niches:")
    top_niches = etf_analytics.get_top_niches(limit=5)
    for i, niche in enumerate(top_niches, 1):
        print(f"   {i}. {niche['niche']}: {niche['stock_count']} stocks, "
              f"RS {niche['avg_rs_rating']:.1f}, Score {niche['strength_score']:.1f}")
    
    # Test niche details
    if top_niches:
        print(f"\n🔍 Details for '{top_niches[0]['niche']}':")
        details = etf_analytics.get_niche_details(top_niches[0]['niche'])
        print(f"   Total stocks: {details['total_stocks']}")
        print(f"   Avg RS rating: {details['avg_rs_rating']:.1f}")
        print(f"   High RS count: {details['high_rs_count']}")
        print(f"   Top 3 stocks by RS:")
        for stock in details['stock_details'][:3]:
            print(f"     {stock['ticker']}: RS {stock['rs_rating']:.1f} ({stock['rs_category']})")
    
    print("\n✅ ETF Analytics test completed!")

if __name__ == "__main__":
    test_analytics()
