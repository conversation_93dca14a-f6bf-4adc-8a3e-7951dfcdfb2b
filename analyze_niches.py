import pandas as pd

df = pd.read_csv('src/data/etf-data/theme_etfs.csv')

print('=== Stock → Unique Niches Analysis ===')
for stock in df['stock_ticker'].unique()[:5]:
    niches = df[df['stock_ticker'] == stock]['factset_niche'].unique()
    print(f'{stock}: {list(niches)}')

print(f'\n=== Summary ===')
print(f'Total stocks: {df["stock_ticker"].nunique()}')
print(f'Total ETFs: {df["ticker"].nunique()}')
print(f'Total combinations: {len(df)}')
print(f'Unique niches: {df["factset_niche"].nunique()}')
print(f'All niches: {sorted(df["factset_niche"].unique())}')

print(f'\n=== Stock-Niche Mapping (Optimized for Stock Queries) ===')
stock_niches = df.groupby('stock_ticker')['factset_niche'].apply(lambda x: sorted(x.unique())).to_dict()
for stock, niches in list(stock_niches.items())[:3]:
    print(f'{stock}: {niches}')
