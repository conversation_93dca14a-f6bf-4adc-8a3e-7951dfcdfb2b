#!/usr/bin/env python3
"""
Data Manager for ETF Theme Collector
Utilities to manage CSV, cache, and ignore list files
"""

import json
import os
import pandas as pd
import sys
from datetime import datetime

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

# Configuration
DATA_DIR = os.path.join(src_dir, "data", "etf-data")
CSV_FILE = os.path.join(DATA_DIR, "theme_etfs.csv")
IGNORE_LIST_FILE = os.path.join(DATA_DIR, "etf_ignore_list.json")
PROCESSED_ETFS_FILE = os.path.join(DATA_DIR, "processed_etfs.json")

class ETFDataManager:
    """Manage ETF data files"""

    def __init__(self):
        os.makedirs(DATA_DIR, exist_ok=True)

    def show_status(self):
        """Show status of all data files"""

        print("ETF Data Manager - Status")
        print("=" * 40)
        print(f"Data directory: {DATA_DIR}")
        print()

        # CSV file
        if os.path.exists(CSV_FILE):
            df = pd.read_csv(CSV_FILE)
            print(f"📊 CSV File: {len(df)} theme ETFs")
            print(f"   File: {CSV_FILE}")

            # Show breakdown by stock
            if 'stock_ticker' in df.columns:
                stock_counts = df['stock_ticker'].value_counts()
                print(f"   Breakdown by stock:")
                for stock, count in stock_counts.head(10).items():
                    print(f"     {stock}: {count} ETFs")
        else:
            print(f"📊 CSV File: Not found")

        print()

        # Ignore list
        if os.path.exists(IGNORE_LIST_FILE):
            with open(IGNORE_LIST_FILE, 'r') as f:
                ignore_data = json.load(f)
                etfs = ignore_data.get('etfs', [])
                last_updated = ignore_data.get('last_updated', 'Unknown')
                print(f"🚫 Ignore List: {len(etfs)} ETFs")
                print(f"   File: {IGNORE_LIST_FILE}")
                print(f"   Last updated: {last_updated}")
                if etfs:
                    print(f"   ETFs: {', '.join(etfs)}")
        else:
            print(f"🚫 Ignore List: Not found")

        print()

        # Cache file
        if os.path.exists(PROCESSED_ETFS_FILE):
            with open(PROCESSED_ETFS_FILE, 'r') as f:
                cache_data = json.load(f)

                # Handle both old format (dict) and new format (minimal)
                if isinstance(cache_data, dict) and 'processed_etfs' in cache_data:
                    # New minimal format
                    processed_count = len(cache_data.get('processed_etfs', []))
                    theme_count = len(cache_data.get('theme_etfs', []))
                    non_theme_count = len(cache_data.get('non_theme_etfs', []))
                    last_updated = cache_data.get('last_updated', 'Unknown')

                    print(f"💾 Cache File: {processed_count} processed ETFs (minimal format)")
                    print(f"   File: {PROCESSED_ETFS_FILE}")
                    print(f"   Theme ETFs: {theme_count}")
                    print(f"   Non-theme ETFs: {non_theme_count}")
                    print(f"   Last updated: {last_updated}")
                else:
                    # Old format
                    print(f"💾 Cache File: {len(cache_data)} processed ETFs (old format)")
                    print(f"   File: {PROCESSED_ETFS_FILE}")

                    # Count theme vs non-theme ETFs in old format
                    theme_count = 0
                    for etf_data in cache_data.values():
                        if isinstance(etf_data, dict) and 'Theme' in etf_data.get('factset_focus', ''):
                            theme_count += 1

                    print(f"   Theme ETFs: {theme_count}")
                    print(f"   Non-theme ETFs: {len(cache_data) - theme_count}")
        else:
            print(f"💾 Cache File: Not found")

    def clear_cache(self):
        """Clear the processed ETFs cache"""

        if os.path.exists(PROCESSED_ETFS_FILE):
            os.remove(PROCESSED_ETFS_FILE)
            print(f"✅ Cleared cache file: {PROCESSED_ETFS_FILE}")
        else:
            print(f"⚠️  Cache file not found: {PROCESSED_ETFS_FILE}")

    def clear_ignore_list(self):
        """Clear the ignore list"""

        if os.path.exists(IGNORE_LIST_FILE):
            os.remove(IGNORE_LIST_FILE)
            print(f"✅ Cleared ignore list: {IGNORE_LIST_FILE}")
        else:
            print(f"⚠️  Ignore list not found: {IGNORE_LIST_FILE}")

    def backup_data(self):
        """Create backup of all data files"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = os.path.join(DATA_DIR, f"backup_{timestamp}")
        os.makedirs(backup_dir, exist_ok=True)

        files_backed_up = 0

        for file_path, file_name in [
            (CSV_FILE, "theme_etfs.csv"),
            (IGNORE_LIST_FILE, "etf_ignore_list.json"),
            (PROCESSED_ETFS_FILE, "processed_etfs.json")
        ]:
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, file_name)
                import shutil
                shutil.copy2(file_path, backup_path)
                files_backed_up += 1
                print(f"✅ Backed up: {file_name}")

        print(f"📦 Backup created: {backup_dir}")
        print(f"   Files backed up: {files_backed_up}")

    def export_csv_summary(self):
        """Export a summary of the CSV data"""

        if not os.path.exists(CSV_FILE):
            print(f"⚠️  CSV file not found: {CSV_FILE}")
            return

        df = pd.read_csv(CSV_FILE)

        # Create summary
        summary = {
            'total_etfs': len(df),
            'unique_stocks': df['stock_ticker'].nunique() if 'stock_ticker' in df.columns else 0,
            'by_stock': df['stock_ticker'].value_counts().to_dict() if 'stock_ticker' in df.columns else {},
            'by_niche': df['factset_niche'].value_counts().to_dict() if 'factset_niche' in df.columns else {},
            'by_category': df['factset_category'].value_counts().to_dict() if 'factset_category' in df.columns else {}
        }

        # Save summary
        summary_file = os.path.join(DATA_DIR, "theme_etfs_summary.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"📈 Summary exported: {summary_file}")
        print(f"   Total ETFs: {summary['total_etfs']}")
        print(f"   Unique stocks: {summary['unique_stocks']}")

def main():
    """Main function"""

    manager = ETFDataManager()

    if len(sys.argv) < 2:
        print("ETF Data Manager")
        print("Usage: python data_manager.py <command>")
        print()
        print("Commands:")
        print("  status      - Show status of all data files")
        print("  clear-cache - Clear the processed ETFs cache")
        print("  clear-ignore - Clear the ignore list")
        print("  backup      - Create backup of all data files")
        print("  summary     - Export CSV summary")
        return

    command = sys.argv[1].lower()

    if command == "status":
        manager.show_status()
    elif command == "clear-cache":
        manager.clear_cache()
    elif command == "clear-ignore":
        manager.clear_ignore_list()
    elif command == "backup":
        manager.backup_data()
    elif command == "summary":
        manager.export_csv_summary()
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
