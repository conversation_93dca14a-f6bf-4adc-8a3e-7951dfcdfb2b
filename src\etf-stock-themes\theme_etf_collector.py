#!/usr/bin/env python3
"""
Theme ETF Collector
Finds theme ETFs that hold specific stocks and saves to CSV
"""

import sys
import os
import csv
import asyncio
import aiohttp
import pandas as pd
from pathlib import Path

# Add src to path to import scrapper functions
sys.path.append('src')
from util.scrapper import etfHoldingsETFDBdotCOM, get_etf_profile_data

CSV_FILE = "theme_etfs.csv"

# CSV columns based on the JSON structure
CSV_COLUMNS = [
    'stock_ticker',
    'ticker',
    'etf_name',
    'category',
    'factset_segment',
    'factset_category',
    'factset_focus',
    'factset_niche',
    'factset_strategy',
    'factset_weighting_scheme'
]

def load_existing_etfs():
    """Load existing ETFs from CSV to avoid duplicates"""
    if not os.path.exists(CSV_FILE):
        return set()

    try:
        df = pd.read_csv(CSV_FILE)
        return set(df['ticker'].tolist()) if 'ticker' in df.columns else set()
    except Exception as e:
        print(f"Error loading existing CSV: {e}")
        return set()

def save_etf_to_csv(etf_data, stock_ticker):
    """Save ETF data to CSV"""

    # Check if file exists
    file_exists = os.path.exists(CSV_FILE)

    # Prepare row data
    row_data = {
        'stock_ticker': stock_ticker,
        'ticker': etf_data.get('ticker', ''),
        'etf_name': etf_data.get('etf_name', ''),
        'category': etf_data.get('category', ''),
        'factset_segment': etf_data.get('factset_segment', ''),
        'factset_category': etf_data.get('factset_category', ''),
        'factset_focus': etf_data.get('factset_focus', ''),
        'factset_niche': etf_data.get('factset_niche', ''),
        'factset_strategy': etf_data.get('factset_strategy', ''),
        'factset_weighting_scheme': etf_data.get('factset_weighting_scheme', '')
    }

    # Write to CSV
    with open(CSV_FILE, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=CSV_COLUMNS)

        # Write header if file is new
        if not file_exists:
            writer.writeheader()

        writer.writerow(row_data)

async def process_stock(stock_ticker):
    """Process a single stock to find theme ETFs"""

    print(f"\n=== Processing {stock_ticker} ===")

    # Load existing ETFs to avoid duplicates
    existing_etfs = load_existing_etfs()
    print(f"Found {len(existing_etfs)} existing ETFs in CSV")

    # Get ETFs that hold this stock
    async with aiohttp.ClientSession() as session:
        print(f"Getting ETFs that hold {stock_ticker}...")
        etf_holdings_df = await etfHoldingsETFDBdotCOM(session, stock_ticker)

    if etf_holdings_df.empty:
        print(f"No ETFs found for {stock_ticker}")
        return

    print(f"Found {len(etf_holdings_df)} ETFs holding {stock_ticker}")

    # Get ETF tickers from the 'Ticker' column
    etf_tickers = []
    if 'Ticker' in etf_holdings_df.columns:
        etf_tickers = etf_holdings_df['Ticker'].tolist()
    elif 'ticker' in etf_holdings_df.columns:
        etf_tickers = etf_holdings_df['ticker'].tolist()
    else:
        print(f"Warning: Could not find ticker column in dataframe. Columns: {etf_holdings_df.columns.tolist()}")
        return

    print(f"ETF tickers to process: {etf_tickers[:5]}...")  # Show first 5

    theme_etfs_found = 0
    new_etfs_added = 0

    # Process each ETF
    for etf_ticker in etf_tickers:
        if pd.isna(etf_ticker) or not etf_ticker:
            continue

        etf_ticker = str(etf_ticker).strip()

        # Skip if already in CSV
        if etf_ticker in existing_etfs:
            print(f"  {etf_ticker}: Already in CSV, skipping")
            continue

        print(f"  Processing {etf_ticker}...")

        # Get ETF profile data
        etf_data = get_etf_profile_data(etf_ticker)

        # Check if it's a theme ETF
        if etf_data.get('factset_focus') == 'Theme':
            theme_etfs_found += 1
            print(f"  ✅ {etf_ticker}: Theme ETF - {etf_data.get('etf_name', 'Unknown')}")
            print(f"     Niche: {etf_data.get('factset_niche', 'Unknown')}")

            # Save to CSV
            save_etf_to_csv(etf_data, stock_ticker)
            existing_etfs.add(etf_ticker)  # Add to memory set to avoid duplicates in this run
            new_etfs_added += 1
        else:
            print(f"  ❌ {etf_ticker}: Not a theme ETF (focus: {etf_data.get('factset_focus', 'Unknown')})")

    print(f"\n{stock_ticker} Summary:")
    print(f"  Theme ETFs found: {theme_etfs_found}")
    print(f"  New ETFs added to CSV: {new_etfs_added}")

async def main():
    """Main function"""

    print("Theme ETF Collector")
    print("=" * 50)

    # Test with the specified stocks
    test_stocks = ["OKLO", "NBIS"]

    for stock in test_stocks:
        await process_stock(stock)

    print(f"\n=== Final Results ===")
    if os.path.exists(CSV_FILE):
        df = pd.read_csv(CSV_FILE)
        print(f"Total theme ETFs in CSV: {len(df)}")
        print(f"CSV file: {CSV_FILE}")

        # Show a sample of what was found
        if len(df) > 0:
            print("\nSample entries:")
            print(df[['stock_ticker', 'ticker', 'etf_name', 'factset_niche']].head())
    else:
        print("No theme ETFs found")

if __name__ == "__main__":
    asyncio.run(main())
