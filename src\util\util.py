# include all util methods here
import datetime
import pandas as pd
from indicators.calc_ATR import calcATR
import numpy as np
from datetime import date
from datetime import timedelta

from util.TTM import calculate_squeeze_momentum

#method to normalize data frames when comparing tickers
def normalizeDF(ticker1Df, ticker2Df):
    ticker1DFCount  = ticker1Df.shape[0]
    ticker2DFCount  = ticker2Df.shape[0]
    ticker1Df.sort_values(by='Date', ascending=False,inplace=True)
    ticker2Df.sort_values(by='Date', ascending=False,inplace=True)

    if(ticker1DFCount == ticker2DFCount):
        return ticker1Df,ticker2Df
    elif(ticker1DFCount < ticker2DFCount):
        return ticker1Df[:ticker1DFCount],ticker2Df[:ticker1DFCount]
    else:
        return ticker1Df[:ticker2DFCount],ticker2Df[:ticker2DFCount]



def getEWM(df, period, key):
    return round(df[key].ewm(span=period, adjust=False).mean(),2)


def getWM(df, period, key):
    return round(df[key].rolling(period).mean() ,2)


# def calcSlope(df,key,lookbackperiod):
#      df['slope_'+ key]=(df[key].diff(periods = -1 * lookbackperiod))/lookbackperiod;
#      return df

def calcExtension(df, key):
    """
    Calculate extension as percentage difference between Close and the moving average.

    Args:
        df: DataFrame containing price data
        key: Moving average column name (e.g., '20', '50', '100', '200')

    Returns:
        DataFrame with extension column added
    """
    try:
        # Check if the DataFrame has MultiIndex columns
        if isinstance(df.columns, pd.MultiIndex):
            # Get the ticker from the MultiIndex
            ticker = df.columns.get_level_values('Ticker')[0]

            # Extract values directly as numpy arrays for better performance
            close_values = df[('Close', ticker)].values
            ma_values = df[key].values

            # Calculate the extension as a percentage using vectorized operations
            # This is more efficient than Series operations
            with np.errstate(divide='ignore', invalid='ignore'):  # Ignore division by zero warnings
                ext_values = 100 * (close_values - ma_values) / ma_values

            # Replace any NaN or inf values with 0
            ext_values = np.nan_to_num(ext_values, nan=0.0, posinf=0.0, neginf=0.0)

            # Add the result as a new column
            df['ext_'+ key] = ext_values
        else:
            # Regular DataFrame - use pandas operations for simplicity
            close_series = df['Close']
            ma_series = df[key]

            # Calculate the extension as a percentage
            df['ext_'+ key] = 100 * (close_series - ma_series) / ma_series.replace(0, np.nan).fillna(close_series)
    except Exception as e:
        print(f"Warning: Extension calculation for {key} failed: {e}")
        # Use placeholder if calculation fails
        df['ext_'+ key] = 0

    return df

def calcSlope(df, key, lookbackperiod):
    """Calculate the slope of a given column over a lookback period.

    This implementation uses numpy arrays to avoid DataFrame operation issues with newer yfinance.
    """
    slopes = []
    # Convert to numpy array for consistent behavior
    values = df[key].to_numpy()

    for i in range(lookbackperiod, len(df)):
        y = values[i - lookbackperiod:i]  # Get values for the lookback period
        x = np.arange(lookbackperiod)     # Index values for regression
        slope = np.polyfit(x, y, 1)[0]    # Linear regression slope
        slopes.append(slope)

    # Pad initial values with NaN
    result = [np.nan] * lookbackperiod + slopes
    # Ensure the result has the same length as the dataframe
    if len(result) > len(df):
        result = result[:len(df)]
    elif len(result) < len(df):
        result = result + [np.nan] * (len(df) - len(result))

    df['slope_'+ key] = result


# Helper functions for MultiIndex handling
def _get_multiindex_series(df, column_name):
    """Helper to get series from MultiIndex or regular DataFrame."""
    if isinstance(df.columns, pd.MultiIndex):
        ticker = df.columns.get_level_values('Ticker')[0]
        return df[(column_name, ticker)]
    else:
        return df[column_name]


def _handle_multiindex_calculation(df, calculation_func, *args, **kwargs):
    """Helper to handle MultiIndex vs regular DataFrame calculations."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            return calculation_func(df, ticker, *args, **kwargs)
        else:
            return calculation_func(df, None, *args, **kwargs)
    except Exception as e:
        print(f"Warning: Calculation failed: {e}")
        return None


# Focused technical indicator functions
def add_moving_averages(df):
    """Calculate and add exponential and simple moving averages."""
    ema_periods = [5, 8, 10, 20, 30]
    sma_periods = [50, 100, 150, 200]

    for period in ema_periods:
        df[str(period)] = getEWM(df, period, 'Close')

    for period in sma_periods:
        df[str(period)] = getWM(df, period, 'Close')

    return df


def _calculate_dollar_volume(df, ticker=None):
    """Helper to calculate dollar volume in millions."""
    if ticker:
        vol_values = df['20Volume'].values
        close_values = df[('Close', ticker)].values
    else:
        vol_values = df['20Volume'].values
        close_values = df['Close'].values

    return (vol_values / 1e6) * close_values


def _calculate_ud_ratio(df):
    """Helper to calculate Up/Down volume ratio."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            close_series = df[('Close', ticker)]
            volume_series = df[('Volume', ticker)]
        else:
            close_series = df['Close']
            volume_series = df['Volume']

        df['UpVolume'] = np.where(close_series > close_series.shift(1), volume_series, 0)
        df['DownVolume'] = np.where(close_series < close_series.shift(1), volume_series, 0)
        df['UDRatio'] = df['UpVolume'].rolling(window=50).sum() / df['DownVolume'].rolling(window=50).sum()
    except Exception as e:
        print(f"Warning: UDRatio calculation failed: {e}")
        df['UpVolume'] = 0
        df['DownVolume'] = 0
        df['UDRatio'] = 1


def add_volume_indicators(df):
    """Calculate volume-based technical indicators."""
    df['ATR'] = calcATR(df, 21)
    df['20Volume'] = getWM(df, 20, 'Volume')

    # Calculate $VolumeM
    try:
        dollar_volume = _handle_multiindex_calculation(df, _calculate_dollar_volume)
        if dollar_volume is not None:
            df['$VolumeM'] = dollar_volume
        else:
            df['$VolumeM'] = df['20Volume'] / 1e6
    except Exception as e:
        print(f"Warning: $VolumeM calculation failed: {e}")
        df['$VolumeM'] = df['20Volume'] / 1e6

    # Calculate Up/Down Volume Ratio
    _calculate_ud_ratio(df)
    return df


def _calculate_adrp(df):
    """Helper to calculate Average Daily Range Percentage."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            high_series = df[('High', ticker)]
            low_series = df[('Low', ticker)]
        else:
            high_series = df['High']
            low_series = df['Low']

        df['ADRP'] = 100 * ((high_series / low_series).rolling(window=20).mean() - 1)
    except Exception as e:
        print(f"Warning: ADRP calculation failed: {e}")
        df['ADRP'] = 0


def _calculate_inside_days(df):
    """Helper to calculate inside day patterns."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            high_prices = df[('High', ticker)]
            low_prices = df[('Low', ticker)]
        else:
            high_prices = df['High']
            low_prices = df['Low']

        prev_high = high_prices.shift(1)
        prev_low = low_prices.shift(1)
        inside_day = (high_prices < prev_high) & (low_prices > prev_low)
        df['inside_day'] = inside_day.fillna(False)
    except Exception as e:
        print(f"Warning: Inside Day calculation failed: {e}")
        df['inside_day'] = False


def _calculate_volume_pocket_pivots(df):
    """Helper to calculate Volume Pocket Pivots."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            open_prices = df[('Open', ticker)]
            close_prices = df[('Close', ticker)]
            high_prices = df[('High', ticker)]
            low_prices = df[('Low', ticker)]
            volume_series = df[('Volume', ticker)]
        else:
            open_prices = df['Open']
            close_prices = df['Close']
            high_prices = df['High']
            low_prices = df['Low']
            volume_series = df['Volume']

        # Condition 1: Up day (Close > Open)
        up_day = close_prices > open_prices

        # Condition 2: Volume > max volume of previous 5 down days
        down_days = (close_prices < open_prices) | (close_prices < close_prices.shift(1))
        max_down_volume = pd.Series(index=df.index, dtype=float)

        for i in range(len(df)):
            if i < 5:
                prev_down_volumes = volume_series.iloc[:i][down_days.iloc[:i]]
            else:
                prev_down_volumes = volume_series.iloc[i-5:i][down_days.iloc[i-5:i]]

            if len(prev_down_volumes) > 0:
                max_down_volume.iloc[i] = prev_down_volumes.max()
            else:
                max_down_volume.iloc[i] = 0

        volume_exceeds_down = volume_series > max_down_volume

        # Condition 3: Close in upper two-thirds of daily range
        upper_two_thirds = close_prices >= (low_prices + (2/3) * (high_prices - low_prices))

        df['volume_pocket_pivot'] = up_day & volume_exceeds_down & upper_two_thirds
        df['volume_pocket_pivot'] = df['volume_pocket_pivot'].fillna(False)
        df['max_down_volume_5d'] = max_down_volume

    except Exception as e:
        print(f"Warning: Volume Pocket Pivot calculation failed: {e}")
        df['volume_pocket_pivot'] = False
        df['max_down_volume_5d'] = 0


def add_pattern_indicators(df):
    """Calculate pattern recognition indicators."""
    _calculate_adrp(df)
    _calculate_inside_days(df)
    _calculate_volume_pocket_pivots(df)
    return df


def _calculate_ttm_squeeze(df):
    """Helper to calculate TTM Squeeze Momentum."""
    try:
        if isinstance(df.columns, pd.MultiIndex):
            ticker = df.columns.get_level_values('Ticker')[0]
            high_values = df[('High', ticker)].values
            low_values = df[('Low', ticker)].values
            close_values = df[('Close', ticker)].values

            temp_df = pd.DataFrame({
                'High': high_values,
                'Low': low_values,
                'Close': close_values
            }, index=df.index)

            df['TTM'] = calculate_squeeze_momentum(temp_df)
        else:
            df['TTM'] = calculate_squeeze_momentum(df)
    except Exception as e:
        print(f"Warning: TTM calculation failed: {e}")
        df['TTM'] = False


def _calculate_slope_indicators(df):
    """Helper to calculate slope indicators for various periods."""
    slope_configs = [
        ('5', 14), ('10', 14), ('20', 14),
        ('50', 20), ('150', 20), ('200', 20),
        ('UDRatio', 50)
    ]

    for key, period in slope_configs:
        calcSlope(df, key, period)


def _calculate_standard_deviation_moves(df):
    """Helper to calculate standard deviation moves."""
    try:
        close_series = _get_multiindex_series(df, 'Close')

        df['Price_Change'] = close_series.diff()
        df['StdDev_Change_30D'] = df['Price_Change'].rolling(window=30).std()
        df['StdDev_Change_60D'] = df['Price_Change'].rolling(window=60).std()
        df['StdDev_Change_90D'] = df['Price_Change'].rolling(window=90).std()

        df['SD_Move_30D'] = (df['Price_Change'] / df['StdDev_Change_30D']).abs().round(2)
        df['SD_Move_60D'] = (df['Price_Change'] / df['StdDev_Change_60D']).abs().round(2)
        df['SD_Move_90D'] = (df['Price_Change'] / df['StdDev_Change_90D']).abs().round(2)
    except Exception as e:
        print(f"Warning: SD Move calculation failed: {e}")
        df['Price_Change'] = 0
        df['StdDev_Change_30D'] = 0
        df['StdDev_Change_60D'] = 0
        df['StdDev_Change_90D'] = 0
        df['SD_Move_30D'] = 0
        df['SD_Move_60D'] = 0
        df['SD_Move_90D'] = 0


def _calculate_ibd_ants_indicator(df):
    """
    Calculate IBD Ants Indicator for institutional accumulation detection.

    Criteria:
    - Stock must have closed higher in at least 12 of the past 15 days
    - Volume should have increased by at least 20-25% over the past 15 days
    - Stock price should have gained 20% or more over the same 15-day period

    When all conditions are met, this indicates strong institutional buying.
    """
    try:
        close_series = _get_multiindex_series(df, 'Close')
        volume_series = _get_multiindex_series(df, 'Volume')

        # Calculate daily price changes for up/down day counting
        daily_changes = close_series.diff()

        # Initialize result arrays
        ants_signal = []
        ants_strength = []

        # Calculate rolling 15-day metrics using pandas rolling functions
        for i in range(len(df)):
            if i < 14:  # Need at least 15 days of data
                ants_signal.append(False)
                ants_strength.append(0.0)
                continue

            # Get 15-day window (from i-14 to i, inclusive)
            window_close = close_series.iloc[i-14:i+1]
            window_volume = volume_series.iloc[i-14:i+1]
            window_changes = daily_changes.iloc[i-14:i+1]

            # Condition 1: Count up days in the 15-day window
            up_days = (window_changes > 0).sum()

            # Condition 2: Price gained 20% or more over 15-day period
            price_change_pct = ((window_close.iloc[-1] / window_close.iloc[0]) - 1) * 100

            # Condition 3: Volume increased by 20-25% over 15-day period
            # Compare recent 5-day average to early 5-day average
            recent_vol_avg = window_volume.iloc[-5:].mean()
            earlier_vol_avg = window_volume.iloc[:5].mean()

            if earlier_vol_avg > 0:
                volume_increase_pct = ((recent_vol_avg / earlier_vol_avg) - 1) * 100
            else:
                volume_increase_pct = 0

            # Check all conditions
            condition_1 = up_days >= 12  # At least 12 up days out of 15 possible
            condition_2 = price_change_pct >= 20.0  # 20% price gain
            condition_3 = volume_increase_pct >= 20.0  # 20% volume increase

            # Ants signal is True when all conditions are met
            signal = condition_1 and condition_2 and condition_3
            ants_signal.append(signal)

            # Calculate strength score (0-100) based on how strongly conditions are met
            if signal:
                up_days_score = min(100, (up_days / 15) * 100)  # Normalize to 0-100
                price_score = min(100, (price_change_pct / 30) * 100)  # 30% = 100 score
                volume_score = min(100, (volume_increase_pct / 50) * 100)  # 50% = 100 score
                strength = (up_days_score + price_score + volume_score) / 3
            else:
                strength = 0.0

            ants_strength.append(round(strength, 1))

        # Add results to DataFrame
        df['IBD_Ants_Signal'] = ants_signal
        df['IBD_Ants_Strength'] = ants_strength

        # Calculate consecutive days with Ants signal
        signal_series = pd.Series(ants_signal, index=df.index)
        df['IBD_Ants_Consecutive'] = (
            signal_series
            .groupby((~signal_series).cumsum())
            .cumsum()
        )

    except Exception as e:
        print(f"Warning: IBD Ants Indicator calculation failed: {e}")
        df['IBD_Ants_Signal'] = False
        df['IBD_Ants_Strength'] = 0.0
        df['IBD_Ants_Consecutive'] = 0


def add_momentum_indicators(df):
    """Calculate momentum and trend indicators."""
    _calculate_ttm_squeeze(df)
    _calculate_slope_indicators(df)
    _calculate_standard_deviation_moves(df)
    _calculate_ibd_ants_indicator(df)
    return df


def add_extension_indicators(df):
    """Calculate price extensions from moving averages."""
    extension_periods = ['20', '50', '100', '200']
    for period in extension_periods:
        calcExtension(df, period)
    return df


def calculate_technical_indicators(df):
    """
    Calculate all technical indicators for a DataFrame.

    This function replaces the old loadMovingAverages function with a more
    organized approach that separates different types of indicators.

    Args:
        df: DataFrame containing OHLCV data

    Returns:
        DataFrame with all technical indicators added
    """
    df = add_moving_averages(df)
    df = add_volume_indicators(df)
    df = add_pattern_indicators(df)
    df = add_momentum_indicators(df)
    df = add_extension_indicators(df)

    df.sort_values(by='Date', ascending=False, inplace=True)
    return df

def loadMovingAverages(df):
    """
    Legacy function name maintained for backward compatibility.

    This function now delegates to calculate_technical_indicators which
    provides a cleaner, more modular implementation of the same functionality.

    Args:
        df: DataFrame containing OHLCV data

    Returns:
        DataFrame with all technical indicators added
    """
    return calculate_technical_indicators(df)


def calcSdWeight(val,period):
    standard_deviation = np.std(val[:period], ddof=1)
    #print(val[:200])
    mean_val = np.mean(val[:period])
    #print(val[0],mean_val,standard_deviation)
    return assignWeight(val, mean_val, standard_deviation)

def assignWeight(val, mean_val, standard_deviation):
    #print(val)
    #print(mean_val - standard_deviation)
    if mean_val - (1*standard_deviation) <= val[0] <= mean_val + (1*standard_deviation):
        #print('Within 1 Deviation')
        return 1
    elif mean_val - (2*standard_deviation) <= val[0] <= mean_val + (2*standard_deviation):
        #print('Within 1 Deviation')
        return 2
    elif mean_val - (3*standard_deviation) <= val[0] <= mean_val + (3*standard_deviation):
        #print('Within 1 Deviation')
        return 3

    else:
        return 4


def remove_all_occurrences(list_obj, value):
    while value in list_obj:
        list_obj.remove(value)
    return list_obj



def findExchange(ticker):
    # Use relative path for exchange files
    import os
    nasdaq_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src/data', 'exchange', 'nasdaqlisted.txt')
    nyse_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src/data', 'exchange', 'otherlisted.txt')

    nasdaq = pd.read_csv(nasdaq_path, sep="|")
    nyse = pd.read_csv(nyse_path, sep="|")
    exchange = ''
    if ticker in nasdaq.values:
       exchange = 'NASDAQ'
    elif ticker in nyse.values:
        exchange = 'NYSE'
    else:
        exchange = 'NYSEAMERICAN'
    return exchange

def findLastWed():
    today = date.today()
    offset = (today.weekday() - 2) % 7
    last_wednesday = today - timedelta(days=offset)
    if date.today().weekday() == 2:
        return today.strftime("%Y-%m-%d")
    else:
        return last_wednesday.strftime("%Y-%m-%d")
    #print(t.strftime("%d/%m/%Y was a %A."))
#print(findLastWed())


def time_until_end_of_day(dt=None):
    if dt is None:
        dt = datetime.datetime.now()
    return ((24 - dt.hour - 1) * 60 * 60) + ((60 - dt.minute - 1) * 60) + (60 - dt.second)

#print(time_until_end_of_day())

def read_and_filter_csv(percentile_threshold=78):
    # Read the CSV file using relative path
    import os
    # Try both possible paths for rs_stocks.csv
    rs_data_path_1 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'rs_data', 'rs_stocks.csv')
    rs_data_path_2 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'src', 'data', 'rs_data', 'rs_stocks.csv')

    # Check which path exists
    if os.path.exists(rs_data_path_1):
        rs_data_path = rs_data_path_1
    elif os.path.exists(rs_data_path_2):
        rs_data_path = rs_data_path_2
    else:
        # If neither path exists, try a direct path
        rs_data_path = 'src/data/rs_data/rs_stocks.csv'
        if not os.path.exists(rs_data_path):
            raise FileNotFoundError(f"Could not find rs_stocks.csv in any of the expected locations: {rs_data_path_1}, {rs_data_path_2}, or {rs_data_path}")

    print(f"Using RS data path: {rs_data_path}")
    df = pd.read_csv(rs_data_path)
    # Filter the DataFrame where Percentile > percentile_threshold
    filtered_df = df[df['Percentile'] > percentile_threshold]
    return filtered_df
