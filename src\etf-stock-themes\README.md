# ETF Stock Themes Collector

This module collects and analyzes theme ETFs that hold specific stocks, with advanced features for caching, deduplication, and batch processing.

## Features

✅ **One-to-Many Mapping**: Handles multiple stocks mapping to the same ETF  
✅ **Theme Filtering**: Filters for ETFs where `factset_focus` = "Theme"  
✅ **Batch Processing**: Efficiently processes multiple ETFs  
✅ **Smart Caching**: Avoids reprocessing ETFs already analyzed  
✅ **Ignore List**: Automatically ignores ETFs that fail to process  
✅ **Deduplication**: Only adds new ETFs to avoid duplicates  
✅ **Stock Tracking**: Records which stock led to finding each ETF  

## Directory Structure

```
src/etf-stock-themes/
├── enhanced_theme_etf_collector.py  # Main collector script
├── data_manager.py                  # Data management utilities
├── test_enhanced_collector.py       # Test script
├── README.md                        # This file
└── tests/                           # Test files
    ├── debug_etf_holdings.py
    └── raw_lib_output.py

src/data/etf-data/
├── theme_etfs.csv                   # Main CSV database
├── etf_ignore_list.json            # ETFs to ignore
├── processed_etfs.json             # Cache of processed ETFs
└── backup_*/                       # Backup directories
```

## Quick Start

### 1. Basic Usage

```bash
# Run the collector with default test stocks (OKLO, NBIS)
python src/etf-stock-themes/enhanced_theme_etf_collector.py
```

### 2. Check Data Status

```bash
# Show status of all data files
python src/etf-stock-themes/data_manager.py status
```

### 3. Run Tests

```bash
# Test with different stocks
python src/etf-stock-themes/test_enhanced_collector.py
```

## Data Management

### Commands

```bash
# Show status
python src/etf-stock-themes/data_manager.py status

# Clear cache (forces reprocessing of all ETFs)
python src/etf-stock-themes/data_manager.py clear-cache

# Clear ignore list (retry failed ETFs)
python src/etf-stock-themes/data_manager.py clear-ignore

# Create backup
python src/etf-stock-themes/data_manager.py backup

# Export summary
python src/etf-stock-themes/data_manager.py summary
```

## How It Works

### 1. Stock to ETF Discovery
- Uses `etfHoldingsETFDBdotCOM` to find all ETFs holding a stock
- Extracts ETF tickers from the 'Ticker' column

### 2. ETF Analysis
- Uses `pyetfdb-scraper` library to get detailed ETF data
- Extracts flat JSON with all factset data
- Caches results to avoid reprocessing

### 3. Theme Filtering
- Filters ETFs where `factset_focus` contains "Theme"
- Saves only theme ETFs to the CSV database

### 4. Deduplication
- Tracks existing ETFs in memory
- Only adds new ETFs to avoid duplicates
- Maintains stock-to-ETF mapping for one-to-many relationships

## CSV Output Format

The CSV contains these columns:

- `stock_ticker`: Stock that led to finding this ETF
- `ticker`: ETF symbol
- `etf_name`: ETF name
- `category`: ETF Database category
- `factset_segment`: Factset segment
- `factset_category`: Factset category
- `factset_focus`: Factset focus (always "Theme")
- `factset_niche`: Factset niche (e.g., "Nuclear Energy")
- `factset_strategy`: Factset strategy
- `factset_weighting_scheme`: Factset weighting scheme

## Customization

### Change Test Stocks

Edit the `main()` function in `enhanced_theme_etf_collector.py`:

```python
# Test stocks
test_stocks = ['AAPL', 'NVDA', 'TSLA']  # Add your stocks here
```

### Modify Theme Filtering

Edit the `filter_theme_etfs()` method to change filtering criteria:

```python
def filter_theme_etfs(self, etf_data: Dict[str, dict]) -> Dict[str, dict]:
    theme_etfs = {}
    for ticker, data in etf_data.items():
        factset_focus = data.get('factset_focus', '')
        # Modify this condition as needed
        if 'Theme' in factset_focus:
            theme_etfs[ticker] = data
    return theme_etfs
```

## Dependencies

- `pyetfdb-scraper`: ETF data extraction
- `pandas`: Data manipulation
- `aiohttp`: Async HTTP requests
- `asyncio`: Async processing

## Performance

- **Caching**: Avoids reprocessing ETFs (saves ~2-3 seconds per ETF)
- **Batch Processing**: Processes multiple ETFs efficiently
- **Ignore List**: Skips ETFs that consistently fail
- **Async**: Uses async/await for ETF holdings lookup

## Error Handling

- **Failed ETFs**: Automatically added to ignore list
- **Network Issues**: Graceful handling with retries
- **Data Validation**: Checks for required columns and data
- **File Safety**: Creates backups and validates file operations

## Example Output

```
Enhanced Theme ETF Collector
============================================================
=== Processing OKLO ===
📊 Found 18 ETFs holding OKLO
🔄 Processing 0 new ETFs (skipping 18 already processed/ignored)
🎯 Found 8 theme ETFs
  ⏭️  URA: Already exists
  ✅ NUKZ: Added - Nuclear Energy

OKLO Summary:
  ETFs found: 18
  Theme ETFs: 8
  New ETFs added: 1
  Already existed: 7

============================================================
FINAL SUMMARY
============================================================
Stocks processed: 2
Total ETFs found: 34
Total theme ETFs found: 17
New theme ETFs added to CSV: 1
Total ETFs in CSV: 18
ETFs in ignore list: 1
ETFs in cache: 33
```

## Troubleshooting

### Common Issues

1. **Import Error**: Install pyetfdb-scraper: `pip install pyetfdb-scraper`
2. **No ETFs Found**: Check if stock ticker is valid
3. **Cache Issues**: Clear cache with `data_manager.py clear-cache`
4. **File Permissions**: Ensure write access to `src/data/etf-data/`

### Debug Mode

For debugging, check individual components:

```bash
# Debug ETF holdings lookup
python src/etf-stock-themes/tests/debug_etf_holdings.py

# Check raw library output
python src/etf-stock-themes/tests/raw_lib_output.py
```
