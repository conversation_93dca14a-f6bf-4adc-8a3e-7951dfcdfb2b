import sys
import os
import asyncio
import aiohttp
import pandas as pd
import requests
import json

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from util.scrapper import etfHoldingsETFDBdotCOM

# Ollama configuration
OLLAMA_URL = "http://localhost:11434/api/generate"
MODEL = "qwen3:0.6b"#"gemma3:1b"  # or your installed small model name

def build_prompt(ticker: str, company_description: str, etf_names: list[str]) -> str:
    # This new prompt uses a "Chain-of-Thought" method to improve accuracy.
    template = """You are a highly specialized thematic investment analyst. Your clients are experts who demand precision. They despise generic business jargon (`business`, `economics`) and financial classifications (`smallcaps`, `growth`). They only care about the themes that describe **WHAT THE COMPANY ACTUALLY DOES.** Your reputation depends on filtering out the noise.

---

### ## **Your Task & Thought Process**

For the given company, you will follow a strict three-step process to generate the final output.

**Step 1: Candidate Generation.**
Read the description and ETF list. Brainstorm a raw list of potential keywords. At this stage, you can include anything that comes to mind, even financial terms.

**Step 2: Aggressive Filtering.**
Review your raw list from Step 1. You must now delete any keyword that violates one of the following **non-negotiable** filtering rules:
* **FILTER 1: No Financial/Market Descriptors.** Remove all terms related to market cap, stock performance, or financial classification. Examples to remove: `smallcaps`, `value`, `growth`, `ipo`, `bonds`, `rates`.
* **FILTER 2: No Generic Business Jargon.** Remove all vague, high-level concepts. Examples to remove: `business`, `technology`, `economics`, `industrial`, `company`, `consumer`.
* **FILTER 3: Must Describe the Core Business.** The keyword must describe a core product, service, or industry. Remove themes from secondary applications or weak inferences.

**Step 3: Final Output.**
From the filtered list, select the top 1-5 most precise and relevant tags. Format them into a single, lowercase, comma-separated line. If no ETFs were found, be extra critical and return only the highest-confidence themes from the description.

---

### ## **Example of Execution 1: NVIDIA (NVDA)**
**Description:** Designs graphics processing units (GPUs) for gaming, data centers, and automotive markets. A leader in artificial intelligence hardware.
**ETFs:** VanEck Semiconductor ETF, Global X Robotics & Artificial Intelligence ETF.

**My Thought Process:**
* **Step 1: Candidate Generation.** `GPUs, gaming, data centers, automotive, artificial intelligence, hardware, semiconductors, robotics, automation, technology`
* **Step 2: Aggressive Filtering.**
    * `GPUs`: Keep (core product). Refine to `semiconductors`.
    * `gaming`: Keep (core market).
    * `data centers`: Keep (core market). Refine to `data`.
    * `automotive`: Keep (core market). Refine to `autos`.
    * `artificial intelligence`: Keep (core technology). Refine to `ai`.
    * `hardware`: Too generic. Discard.
    * `semiconductors`: Keep (core product).
    * `robotics`: Keep (strong thematic link).
    * `automation`: Keep (strong thematic link).
    * `technology`: **Discard (violates FILTER 2).**
* **Step 3: Final Output.** `ai, semiconductors, data, robotics, automation, gaming, autos`

### ## **Example of Execution 2: UBER (UBER)**
**Description:** Operates a technology platform for Mobility (ridesharing), Delivery (food, grocery), and Freight (logistics).
**ETFs:** Amplify Travel Tech ETF, ProShares On-Demand ETF, Fidelity Electric Vehicles and Future Transportation ETF.

**My Thought Process:**
* **Step 1: Candidate Generation.** `mobility, ridesharing, delivery, freight, logistics, technology platform, consumer, on-demand, electric vehicles, transportation, ai, automation`
* **Step 2: Aggressive Filtering.**
    * `mobility`: Keep (core service).
    * `ridesharing`, `delivery`, `freight`: All are types of `mobility` or `logistics`. Keep the broader terms.
    * `logistics`: Keep (core service).
    * `technology platform`: Too generic. Discard.
    * `consumer`: Keep (core market).
    * `on-demand`: This is a business model, not a theme. Discard.
    * `electric vehicles`: Related, but not core to Uber's *own* business. Discard.
    * `transportation`: Redundant with `mobility`. Discard.
    * `ai`: Keep (enabling technology).
    * `automation`: Keep (enabling technology).
* **Step 3: Final Output.** `mobility, logistics, consumer, ai, automation`

---

### ## **NOW, EXECUTE THIS TASK:**

**Task:** Tag {ticker}
**Description:** {desc}
**ETFs:** {etfs}

**Your Thought Process:**
* **Step 1: Candidate Generation.**
* **Step 2: Aggressive Filtering.**
* **Step 3: Final Output.**
"""
    return template.format(
        ticker=ticker,
        desc=(company_description or "").strip(),
        etfs=", ".join([e.strip() for e in etf_names]) if etf_names else "None found."
    )

def generate_themes(ticker: str, company_description: str, etf_names: list[str]) -> str:
    prompt = build_prompt(ticker, company_description, etf_names)
    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        # Small-model-friendly knobs:
        "options": {
            "temperature": 0,
            "top_p": 0.9
        }
    }
    try:
        resp = requests.post(OLLAMA_URL, json=payload, timeout=120)
        resp.raise_for_status()
        data = resp.json()
        text = data.get("response", "").strip()
        # light cleanup: ensure lowercase, strip backticks/spaces
        text = text.strip("` \n").lower()
        # optional: split to list
        themes = [t.strip() for t in text.split(",") if t.strip()]
        # dedupe while preserving order
        seen, deduped = set(), []
        for t in themes:
            if t not in seen:
                seen.add(t)
                deduped.append(t)
        return ", ".join(deduped)
    except Exception as e:
        print(f"Error generating themes with Ollama: {e}")
        return "error_generating_themes"

async def get_etf_holdings(session, ticker):
    """Get ETF holdings for a ticker."""
    try:
        result = await etfHoldingsETFDBdotCOM(session, ticker)
        if result is not None and hasattr(result, 'columns') and not result.empty:
            # Get the ETF names (second column, index 1)
            if len(result.columns) > 1:
                etf_values = result.iloc[:, 1].tolist()
                return etf_values
        return []
    except Exception as e:
        print(f"Error getting ETF holdings for {ticker}: {e}")
        return []

async def process_screener_tickers():
    """Process tickers from screener file and generate themes."""

    # Load screener data
    screener_file = 'src/data/screener/filtered_sorted_screener_2025-09-24.xlsx'
    try:
        df = pd.read_excel(screener_file)
        print(f"Loaded {len(df)} rows from screener file")
        print(f"Columns: {df.columns.tolist()}")
    except Exception as e:
        print(f"Error loading screener file: {e}")
        return

    # Check if required columns exist
    if 'Symbol' not in df.columns or 'Description' not in df.columns:
        print("Required columns 'Symbol' and 'Description' not found in screener file")
        return

    # Process each ticker
    results = []

    async with aiohttp.ClientSession() as session:
        for idx, row in df.iterrows():
            ticker = row['Symbol']
            description = row['Description']

            print(f"\nProcessing {ticker} ({idx+1}/{len(df)})")

            # Get ETF holdings
            etf_names = await get_etf_holdings(session, ticker)

            if etf_names:
                print(f"Found {len(etf_names)} ETF holdings for {ticker}")
                print(f"ETFs: {', '.join(etf_names[:3])}{'...' if len(etf_names) > 3 else ''}")

                # Generate themes using Ollama
                themes = generate_themes(ticker, description, etf_names)
                print(f"Generated themes: {themes}")

                results.append({
                    'Ticker': ticker,
                    'Description': description,
                    'ETF_Count': len(etf_names),
                    'ETF_Names': '; '.join(etf_names),
                    'Generated_Themes': themes
                })
            else:
                print(f"No ETF holdings found for {ticker}")
                themes = generate_themes(ticker, description, [])
                results.append({
                    'Ticker': ticker,
                    'Description': description,
                    'ETF_Count': 0,
                    'ETF_Names': '',
                    'Generated_Themes': themes
                })

    # Save results
    if results:
        results_df = pd.DataFrame(results)
        output_file = f'ticker_themes_with_etf_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.csv'
        results_df.to_csv(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        print(f"Processed {len(results)} tickers")

        # Show summary
        with_etf = len([r for r in results if r['ETF_Count'] > 0])
        print(f"Tickers with ETF data: {with_etf}")
        print(f"Tickers without ETF data: {len(results) - with_etf}")
    else:
        print("No results to save")

async def test_single_ticker(ticker="OKLO"):
    """Test with a single ticker for debugging."""

    # Load screener data to get description
    screener_file = 'src/data/screener/filtered_sorted_screener_2025-09-24.xlsx'
    try:
        df = pd.read_excel(screener_file)
        ticker_row = df[df['Symbol'] == ticker]
        if ticker_row.empty:
            print(f"Ticker {ticker} not found in screener file")
            description = f"{ticker} - Test description"
        else:
            description = ticker_row['Description'].iloc[0]
    except Exception as e:
        print(f"Error loading screener file: {e}")
        description = f"{ticker} - Test description"

    print(f"Testing with {ticker}")
    print(f"Description: {description}...")

    async with aiohttp.ClientSession() as session:
        # Get ETF holdings
        etf_names = await get_etf_holdings(session, ticker)

        if etf_names:
            print(f"\nFound {len(etf_names)} ETF holdings:")
            for etf in etf_names:
                print(f"  - {etf}")

            # Generate themes
            themes = generate_themes(ticker, description, etf_names)
            print(f"\nGenerated themes: {themes}")
        else:
            themes = generate_themes(ticker, description, [])
            print(f"No ETF holdings found for {ticker}")
            print(f"\nGenerated themes: {themes}")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--test", help="Test with single ticker", default=None)
    parser.add_argument("--all", action="store_true", help="Process all tickers from screener")
    args = parser.parse_args()

    if args.test:
        asyncio.run(test_single_ticker(args.test))
    elif args.all:
        asyncio.run(process_screener_tickers())
    else:
        # Default: test with OKLO
        asyncio.run(test_single_ticker("HIMS"))
