<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Weekly Screener</title>
  <link rel="stylesheet" href="/ui/styles.css">
</head>
<body>
  <header>
    <div>Weekly Screener</div>
    <a href="/chart" target="_blank" style="color:#60a5fa;">📊 Charts</a>
    <a href="#" onclick="showETFAnalytics()" style="color:#10b981;">🎯 ETF Themes</a>
    <div class="filters">
      <div class="filter-group primary">
        <label>🎯</label>
        <select onchange="updateFilter('signal', this.value)" class="signal-filter" title="Filter by Signal Strength">
          {{signal_options}}
        </select>
      </div>
      <div class="filter-separator"></div>
      <select onchange="updateFilter('sector', this.value)" title="Filter by Sector">
        {{sector_options}}
      </select>
      <select onchange="updateFilter('industry', this.value)" title="Filter by Industry">
        {{industry_options}}
      </select>
      <select onchange="updateFilter('sub_industry', this.value)" title="Filter by Sub-Industry">
        {{sub_industry_options}}
      </select>
      <div class="filter-separator"></div>
      <select onchange="updateFilter('theme', this.value)" title="Filter by ETF Theme" class="theme-filter">
        {{theme_options}}
      </select>
    </div>
    <button onclick="saveWeek()">Save Week</button>
    <a href='?liked=1' style='color:#9cf'>Show Liked Only</a>
    <span class='controls'>
      <button onclick="prevCard()">< Prev</button>
      <button onclick="nextCard()">Next ></button>
      <label style='display:flex; align-items:center; gap:6px;'>
        <input type='checkbox' id='cardOnlyToggle' onchange='toggleCardOnly(this.checked)'> One per page
      </label>
    </span>
  </header>
  <div class="grid">
    {{rows}}
  </div>
  <script src="/ui/main.js"></script>
</body>
</html>
