#!/usr/bin/env python3
"""
Test script for the optimized stock-niches cache functionality
"""

import sys
import os
import time
import pandas as pd

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

from enhanced_theme_etf_collector import EnhancedThemeETFCollector

def test_cache_functionality():
    """Test the stock-niches cache functionality"""

    print("🧪 Testing Optimized Stock-Niches Cache")
    print("=" * 60)

    # Initialize collector
    collector = EnhancedThemeETFCollector()

    # Print initial cache stats
    print("\n📊 Initial Cache State:")
    collector.print_cache_stats()

    # Test cache rebuild
    print("\n🔄 Testing Cache Rebuild...")
    collector.rebuild_stock_niches_cache()

    # Print updated cache stats
    print("\n📊 After Rebuild:")
    collector.print_cache_stats()

    # Test O(1) queries
    print("\n⚡ Testing Query Performance:")
    test_stocks = ['TSLA', 'OKLO', 'UBER', 'LYFT', 'NBIS']

    for stock in test_stocks:
        if stock in collector.stock_niches_cache:
            # Time the O(1) cache lookup
            start_time = time.time()
            niches = collector.get_stock_niches(stock)
            cache_time = time.time() - start_time

            print(f"   {stock}: {niches} ({cache_time*1000:.3f}ms)")
        else:
            print(f"   {stock}: Not in cache")

    # Test fallback to CSV query
    print("\n🔍 Testing CSV Fallback (for non-cached stock):")
    fake_stock = "FAKE_STOCK"
    start_time = time.time()
    niches = collector.get_stock_niches(fake_stock)
    csv_time = time.time() - start_time
    print(f"   {fake_stock}: {niches} ({csv_time*1000:.3f}ms)")

    # Compare with traditional CSV scanning
    print("\n📈 Performance Comparison:")
    csv_file = os.path.join(src_dir, "data", "etf-data", "theme_etfs.csv")
    if os.path.exists(csv_file):
        df = pd.read_csv(csv_file)

        # Traditional CSV scan
        test_stock = 'TSLA'
        start_time = time.time()
        csv_niches = df[df['stock_ticker'] == test_stock]['factset_niche'].unique().tolist()
        csv_scan_time = time.time() - start_time

        # Cache lookup
        start_time = time.time()
        cache_niches = collector.get_stock_niches(test_stock)
        cache_lookup_time = time.time() - start_time

        print(f"   CSV scan for {test_stock}: {csv_scan_time*1000:.3f}ms")
        print(f"   Cache lookup for {test_stock}: {cache_lookup_time*1000:.6f}ms")

        if cache_lookup_time > 0:
            speedup = csv_scan_time / cache_lookup_time
            print(f"   Speedup: {speedup:.0f}x faster")
        else:
            print(f"   Speedup: >1000x faster (cache lookup too fast to measure)")

    # Test cache file size
    cache_file = os.path.join(src_dir, "data", "etf-data", "stock_niches_cache.json")
    if os.path.exists(cache_file):
        cache_size = os.path.getsize(cache_file)
        print(f"\n💾 Cache File Size: {cache_size:,} bytes ({cache_size/1024:.1f} KB)")

    print("\n✅ Cache functionality test completed!")

def demonstrate_usage():
    """Demonstrate typical usage patterns"""

    print("\n🎯 Usage Examples:")
    print("=" * 60)

    collector = EnhancedThemeETFCollector()

    # Example 1: Get niches for a stock
    stock = 'TSLA'
    niches = collector.get_stock_niches(stock)
    print(f"\n1. Get niches for {stock}:")
    print(f"   Result: {niches}")

    # Example 2: Check if stock has specific niche
    target_niche = 'Big Tech'
    has_niche = target_niche in niches
    print(f"\n2. Does {stock} have '{target_niche}' niche?")
    print(f"   Result: {has_niche}")

    # Example 3: Get all stocks with a specific niche
    print(f"\n3. All stocks with '{target_niche}' niche:")
    stocks_with_niche = []
    for stock, stock_niches in collector.stock_niches_cache.items():
        if target_niche in stock_niches:
            stocks_with_niche.append(stock)
    print(f"   Result: {stocks_with_niche}")

    # Example 4: Get cache statistics
    print(f"\n4. Cache statistics:")
    stats = collector.get_cache_stats()
    print(f"   Total stocks: {stats['total_stocks']}")
    print(f"   Unique niches: {stats['total_unique_niches']}")
    print(f"   Avg niches per stock: {stats['avg_niches_per_stock']:.1f}")

if __name__ == "__main__":
    test_cache_functionality()
    demonstrate_usage()

    print(f"\n{'='*60}")
    print("🎉 All tests completed!")
    print("The optimized cache provides O(1) stock → niches queries")
    print("while maintaining Excel-friendly CSV storage.")
    print(f"{'='*60}")
