"""
Test script for the IBD Ants Indicator implementation.
This script demonstrates how to use the new Ants indicator to identify institutional accumulation.
"""

import sys
import os
import pandas as pd
import asyncio
import aiohttp

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from stockdata.data_source import getStockDataV3
from common.http_session import get_session

async def test_ants_indicator():
    """Test the IBD Ants Indicator with sample stocks."""
    print("🐜 Testing IBD Ants Indicator")
    print("=" * 50)

    # Test with some popular stocks
    test_tickers = ['app']

    async with aiohttp.ClientSession() as session:
        for ticker in test_tickers:
            print(f"\n📊 Analyzing {ticker}...")

            try:
                df = await getStockDataV3(session, ticker)

                if df is None or df.empty:
                    print(f"❌ No data available for {ticker}")
                    continue

                # Check if Ants indicators are present
                required_cols = ['IBD_Ants_Signal', 'IBD_Ants_Strength', 'IBD_Ants_Consecutive']
                if not all(col in df.columns for col in required_cols):
                    print(f"❌ Ants indicator columns missing for {ticker}")
                    continue

                # Get recent Ants signals (last 30 days)
                recent_data = df.head(30)

                # Check for any Ants signals in recent period
                ants_days = recent_data[recent_data['IBD_Ants_Signal'] == True]

                if not ants_days.empty:
                    print(f"🐜 ANTS DETECTED for {ticker}!")
                    print(f"   📅 Most recent signal: {ants_days.index[0].strftime('%Y-%m-%d')}")

                    # Safely get scalar values for formatting
                    strength_val = float(ants_days['IBD_Ants_Strength'].iloc[0])
                    consecutive_val = int(ants_days['IBD_Ants_Consecutive'].iloc[0])

                    print(f"   💪 Signal strength: {strength_val:.1f}%")
                    print(f"   🔥 Consecutive days: {consecutive_val}")

                    # Show price and volume performance during signal
                    signal_day = ants_days.iloc[0]

                    # Safely get Close price
                    try:
                        if isinstance(df.columns, pd.MultiIndex):
                            close_cols = [col for col in df.columns if col[0] == 'Close']
                            if close_cols:
                                close_val = float(signal_day[close_cols[0]])
                                print(f"   📈 Close price: ${close_val:.2f}")
                        else:
                            close_val = float(signal_day['Close'])
                            print(f"   📈 Close price: ${close_val:.2f}")
                    except Exception as close_error:
                        print(f"   📈 Close price: [Unable to display: {close_error}]")

                    # Handle volume display safely
                    try:
                        if isinstance(df.columns, pd.MultiIndex):
                            volume_cols = [col for col in df.columns if col[0] == 'Volume']
                            if volume_cols:
                                volume_val = float(signal_day[volume_cols[0]])
                                print(f"   📊 Volume: {volume_val:,.0f}")
                        elif 'Volume' in df.columns:
                            volume_val = float(signal_day['Volume'])
                            print(f"   📊 Volume: {volume_val:,.0f}")
                    except Exception as vol_error:
                        print(f"   📊 Volume: [Unable to display: {vol_error}]")
                else:
                    print(f"   ℹ️ No recent Ants signals for {ticker}")

                # Show summary statistics
                total_signals = int(df['IBD_Ants_Signal'].sum())
                max_strength = float(df['IBD_Ants_Strength'].max())
                max_consecutive = int(df['IBD_Ants_Consecutive'].max())

                print(f"   📊 Historical summary:")
                print(f"      Total Ants days: {total_signals}")
                print(f"      Max strength: {max_strength:.1f}%")
                print(f"      Max consecutive: {max_consecutive} days")

            except Exception as e:
                print(f"❌ Error analyzing {ticker}: {e}")

async def find_ants_stocks():
    """Find stocks with current Ants signals."""
    print(f"\n🔍 Searching for stocks with active Ants signals...")
    print("=" * 50)

    # Sample of stocks to check (in a real implementation, you'd use your screener)
    sample_tickers = [
        'TSLA'
    ]

    ants_stocks = []

    async with aiohttp.ClientSession() as session:
        for ticker in sample_tickers:
            try:
                df = await getStockDataV3(session, ticker)

                if df is not None and not df.empty and 'IBD_Ants_Signal' in df.columns:
                    # Check if there's an Ants signal in the last 5 days
                    recent_ants = df.head(5)['IBD_Ants_Signal'].any()

                    if recent_ants:
                        latest_signal = df[df['IBD_Ants_Signal']].iloc[0]
                        ants_stocks.append({
                            'Ticker': ticker,
                            'Date': latest_signal.name.strftime('%Y-%m-%d'),
                            'Strength': latest_signal['IBD_Ants_Strength'],
                            'Consecutive': latest_signal['IBD_Ants_Consecutive'],
                            'Price': latest_signal['Close']
                        })

                        print(f"🐜 {ticker}: Strength {latest_signal['IBD_Ants_Strength']:.1f}%, "
                              f"Price ${latest_signal['Close']:.2f}")

            except Exception as e:
                print(f"   ❌ Error checking {ticker}: {e}")

    if ants_stocks:
        print(f"\n✅ Found {len(ants_stocks)} stocks with recent Ants signals!")
        print("\n📋 Summary:")
        print("-" * 60)
        print(f"{'Ticker':<8} {'Date':<12} {'Strength':<10} {'Consecutive':<12} {'Price':<10}")
        print("-" * 60)

        for stock in sorted(ants_stocks, key=lambda x: x['Strength'], reverse=True):
            print(f"{stock['Ticker']:<8} {stock['Date']:<12} {stock['Strength']:<10.1f} "
                  f"{stock['Consecutive']:<12} ${stock['Price']:<10.2f}")
    else:
        print("   ℹ️ No stocks found with recent Ants signals in the sample.")

async def main():
    """Run Ants indicator tests."""
    try:
        await test_ants_indicator()
        await find_ants_stocks()

        print("\n" + "=" * 50)
        print("✅ IBD Ants Indicator testing completed!")
        print("\n💡 Usage Tips:")
        print("   🐜 Ants signals indicate strong institutional accumulation")
        print("   📈 Look for signals after consolidation periods")
        print("   ⚠️ Can also signal tops in extended moves")
        print("   🎯 Use with other technical analysis for best results")

    except Exception as e:
        print(f"❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())