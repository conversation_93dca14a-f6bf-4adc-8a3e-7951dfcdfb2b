#!/usr/bin/env python3
"""
Test script for the enhanced theme ETF collector
"""

import asyncio
import os
import sys

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

from enhanced_theme_etf_collector import EnhancedThemeETFCollector

async def test_single_stock():
    """Test with a single stock"""

    print("Testing Enhanced Theme ETF Collector with single stock")
    print("=" * 60)

    collector = EnhancedThemeETFCollector()

    # Test with a new stock to see if it finds new ETFs
    test_stock = "TSLA"

    result = await collector.process_stock(test_stock)

    print(f"\nResults for {test_stock}:")
    print(f"  ETFs found: {result['etfs_found']}")
    print(f"  Theme ETFs: {result['theme_etfs_found']}")
    print(f"  New ETFs added: {result['new_etfs_added']}")
    print(f"  Already existed: {result['already_existed']}")

    return result

async def test_multiple_stocks():
    """Test with multiple stocks"""

    print("\nTesting Enhanced Theme ETF Collector with multiple stocks")
    print("=" * 60)

    collector = EnhancedThemeETFCollector()

    # Test with a mix of stocks
    test_stocks = ["UBER"]

    results = await collector.process_multiple_stocks(test_stocks)
    collector.display_final_summary(results)

    return results

def test_cache_functionality():
    """Test the caching functionality"""

    print("\nTesting Cache Functionality")
    print("=" * 40)

    collector = EnhancedThemeETFCollector()

    print(f"Existing ETFs in CSV: {len(collector.existing_etfs)}")
    print(f"ETFs in ignore list: {len(collector.ignore_list)}")
    print(f"ETFs in cache: {len(collector.processed_etfs)}")

    # Show some examples from cache
    if collector.theme_etfs:
        print(f"\nSample theme ETFs from cache:")
        for ticker in list(collector.theme_etfs)[:5]:
            print(f"  {ticker}: Theme ETF")

    if collector.non_theme_etfs:
        print(f"\nSample non-theme ETFs from cache:")
        for ticker in list(collector.non_theme_etfs)[:5]:
            print(f"  {ticker}: Non-theme ETF")

    # Show ignore list
    if collector.ignore_list:
        print(f"\nIgnored ETFs: {list(collector.ignore_list)}")

async def main():
    """Main test function"""

    print("Enhanced Theme ETF Collector - Test Suite")
    print("=" * 60)

    # Test 1: Cache functionality
    test_cache_functionality()

    # Test 2: Single stock
    await test_single_stock()

    # Test 3: Multiple stocks
    #await test_multiple_stocks()

    print("\n" + "=" * 60)
    print("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
