import pandas as pd
import json
import os
from collections import defaultdict

# Load current data
df = pd.read_csv('src/data/etf-data/theme_etfs.csv')

print("=== Current Scale Analysis ===")
current_stocks = df['stock_ticker'].nunique()
current_etfs = df['ticker'].nunique()
current_combinations = len(df)
current_niches = df['factset_niche'].nunique()

print(f"Current: {current_stocks} stocks → {current_etfs} ETFs → {current_combinations} combinations")
print(f"Average ETFs per stock: {current_combinations / current_stocks:.1f}")
print(f"Average niches per stock: {df.groupby('stock_ticker')['factset_niche'].nunique().mean():.1f}")

# Extrapolate to 1000 stocks
target_stocks = 1000
etfs_per_stock = current_combinations / current_stocks
niches_per_stock = df.groupby('stock_ticker')['factset_niche'].nunique().mean()

print(f"\n=== Projected Scale (1000 stocks) ===")
projected_combinations = int(target_stocks * etfs_per_stock)
projected_etfs = int(current_etfs * (target_stocks / current_stocks) * 0.7)  # Assuming some ETF overlap

print(f"Projected: {target_stocks} stocks → ~{projected_etfs} ETFs → ~{projected_combinations} combinations")
print(f"Average niches per stock: {niches_per_stock:.1f}")

# Storage analysis
print(f"\n=== Storage Analysis ===")

# Current denormalized CSV size
current_csv_size = os.path.getsize('src/data/etf-data/theme_etfs.csv')
print(f"Current CSV size: {current_csv_size:,} bytes ({current_csv_size/1024:.1f} KB)")

# Projected denormalized size
projected_csv_size = current_csv_size * (projected_combinations / current_combinations)
print(f"Projected denormalized CSV: {projected_csv_size:,} bytes ({projected_csv_size/1024/1024:.1f} MB)")

# Normalized storage calculation
avg_etf_name_length = df['etf_name'].str.len().mean()
avg_category_length = df['category'].str.len().mean()
etf_details_row_size = 50 + avg_etf_name_length + avg_category_length  # Rough estimate

stock_etf_row_size = 20  # stock_ticker + etf_ticker
stock_niche_row_size = 30  # stock_ticker + niche

normalized_etf_details = projected_etfs * etf_details_row_size
normalized_stock_etf = projected_combinations * stock_etf_row_size
normalized_stock_niche = target_stocks * niches_per_stock * stock_niche_row_size

total_normalized = normalized_etf_details + normalized_stock_etf + normalized_stock_niche

print(f"Projected normalized storage:")
print(f"  ETF details: {normalized_etf_details:,} bytes ({normalized_etf_details/1024:.1f} KB)")
print(f"  Stock-ETF mappings: {normalized_stock_etf:,} bytes ({normalized_stock_etf/1024:.1f} KB)")
print(f"  Stock-Niche mappings: {normalized_stock_niche:,} bytes ({normalized_stock_niche/1024:.1f} KB)")
print(f"  Total normalized: {total_normalized:,} bytes ({total_normalized/1024/1024:.1f} MB)")

print(f"\nStorage savings: {((projected_csv_size - total_normalized) / projected_csv_size * 100):.1f}%")

# Query performance analysis
print(f"\n=== Query Performance Analysis ===")
print("Query: get_unique_niches_for_stock(ticker)")
print(f"Denormalized CSV: Scan {projected_combinations:,} rows, filter by stock")
print(f"Normalized stock-niche: Scan {int(target_stocks * niches_per_stock):,} rows, filter by stock")
print(f"JSON cache: O(1) dictionary lookup")

# Memory usage
print(f"\n=== Memory Usage (in-memory caching) ===")
stock_niche_dict_size = target_stocks * niches_per_stock * 50  # Rough estimate
print(f"Stock-niche dictionary: ~{stock_niche_dict_size:,} bytes ({stock_niche_dict_size/1024/1024:.1f} MB)")
