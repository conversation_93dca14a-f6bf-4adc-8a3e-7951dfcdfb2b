#!/usr/bin/env python3

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from stockdata.data_source import getStockDataV3
import pandas as pd

async def debug_pl_pattern():
    """Debug why <PERSON><PERSON> is showing up in IBD ANTS + POCKET PIVOT category"""

    print("🔍 Debugging PL pattern detection...")
    print("=" * 50)

    try:
        # Get stock data for PL
        stock_df = await getStockDataV3('PL', '1y')

        if stock_df is None or stock_df.empty:
            print("❌ No data retrieved for PL")
            return

        print(f"📊 Retrieved {len(stock_df)} days of data for PL")
        print(f"📅 Date range: {stock_df.index[-1]} to {stock_df.index[0]}")

        # Check what columns are available
        print(f"\n📋 Available columns:")
        for col in sorted(stock_df.columns):
            print(f"   {col}")

        # Check the most recent day (index 0) values
        print(f"\n🔍 Most recent day values (index 0):")
        print(f"   Date: {stock_df.index[0]}")

        # Check pattern indicators
        if 'inside_day' in stock_df.columns:
            inside_val = stock_df['inside_day'].iloc[0]
            print(f"   inside_day: {inside_val} (bool: {bool(inside_val)})")
        else:
            print("   ❌ inside_day column not found")

        if 'volume_pocket_pivot' in stock_df.columns:
            pivot_val = stock_df['volume_pocket_pivot'].iloc[0]
            print(f"   volume_pocket_pivot: {pivot_val} (bool: {bool(pivot_val)})")
        else:
            print("   ❌ volume_pocket_pivot column not found")

        if 'IBD_Ants_Signal' in stock_df.columns:
            ants_val = stock_df['IBD_Ants_Signal'].iloc[0]
            print(f"   IBD_Ants_Signal: {ants_val} (bool: {bool(ants_val)})")
        else:
            print("   ❌ IBD_Ants_Signal column not found")

        # Show recent days with any pattern signals
        print(f"\n📈 Recent days with pattern signals:")
        pattern_cols = []
        for col in ['inside_day', 'volume_pocket_pivot', 'IBD_Ants_Signal']:
            if col in stock_df.columns:
                pattern_cols.append(col)

        if pattern_cols:
            # Show last 10 days where any pattern is True
            recent_df = stock_df[pattern_cols].head(10)
            any_signal = recent_df.any(axis=1)
            signal_days = recent_df[any_signal]

            if len(signal_days) > 0:
                print(signal_days)
            else:
                print("   No pattern signals in last 10 days")
        else:
            print("   No pattern columns available")

        # Replicate scanner logic
        print(f"\n🔄 Replicating scanner logic:")
        has_inside_day = 'inside_day' in stock_df.columns and bool(stock_df['inside_day'].iloc[0])
        has_pocket_pivot = 'volume_pocket_pivot' in stock_df.columns and bool(stock_df['volume_pocket_pivot'].iloc[0])
        has_ants = 'IBD_Ants_Signal' in stock_df.columns and bool(stock_df['IBD_Ants_Signal'].iloc[0])

        print(f"   has_inside_day: {has_inside_day}")
        print(f"   has_pocket_pivot: {has_pocket_pivot}")
        print(f"   has_ants: {has_ants}")

        if has_inside_day and has_pocket_pivot and has_ants:
            category = "ALL THREE patterns"
        elif has_inside_day and has_pocket_pivot:
            category = "Inside day + Pocket pivot"
        elif has_ants and has_inside_day:
            category = "Ants + Inside day"
        elif has_ants and has_pocket_pivot:
            category = "Ants + Pocket pivot"  # This is where PL should be
        elif has_inside_day:
            category = "Inside day only"
        elif has_pocket_pivot:
            category = "Pocket pivot only"
        elif has_ants:
            category = "IBD Ants only"
        else:
            category = "No patterns"

        print(f"\n🎯 Final category: {category}")

    except Exception as e:
        print(f"❌ Error debugging PL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_pl_pattern())