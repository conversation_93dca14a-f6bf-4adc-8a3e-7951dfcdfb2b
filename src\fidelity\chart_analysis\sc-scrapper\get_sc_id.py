import asyncio
import os
import re
import sys
from dataclasses import dataclass
from typing import Optional, List

from playwright.async_api import async_playwright, Locator, TimeoutError as PWTimeout

START_URL = "https://stockcharts.com/sc3/ui/?s=AAPL"
IMG_URL_SUBSTR = "/c-sc/sc?"
ID_REGEX = re.compile(r"[?&]i=([A-Za-z0-9_-]+)")

# You can change these defaults via CLI args if you want.
DEFAULT_EMA = "20"
DEFAULT_SMA1 = "50"
DEFAULT_SMA2 = "100"

@dataclass
class Settings:
    symbol: str
    ema: str = DEFAULT_EMA
    sma1: str = DEFAULT_SMA1
    sma2: str = DEFAULT_SMA2
    headless: bool = True
    slow_mo: int = 0
    timeout_ms: int = 60000  # Increased timeout to 60s

def parse_args(argv: List[str]) -> Settings:
    # very small CLI: python get_sc_id.py TSLA --ema 20 --sma1 50 --sma2 100 --headed
    sym = "AAPL"
    ema, sma1, sma2 = DEFAULT_EMA, DEFAULT_SMA1, DEFAULT_SMA2
    headless = True
    slow_mo = 0
    i = 1
    if len(argv) >= 2 and not argv[1].startswith("-"):
        sym = argv[1]
        i = 2
    while i < len(argv):
        if argv[i] == "--ema": ema = argv[i+1]; i += 2
        elif argv[i] == "--sma1": sma1 = argv[i+1]; i += 2
        elif argv[i] == "--sma2": sma2 = argv[i+1]; i += 2
        elif argv[i] == "--headed": headless = False; i += 1
        elif argv[i] == "--slowmo": slow_mo = int(argv[i+1]); i += 2
        else: i += 1
    return Settings(symbol=sym, ema=ema, sma1=sma1, sma2=sma2, headless=headless, slow_mo=slow_mo)

async def select_option_by_text(select: Locator, wanted: str):
    """Safely choose an option by visible text (exact or case-insensitive contains)."""
    await select.wait_for(state="visible", timeout=10000)

    # Try exact label first (Playwright's native path)
    try:
        await select.select_option(label=wanted)
        return
    except Exception:
        pass

    # Fallback: read options, find best match, then select by value or index
    try:
        options = select.locator("option")
        await options.first.wait_for(timeout=5000)
        texts = await options.all_text_contents()
        texts = [t.strip() for t in texts if t.strip()]

        # find exact (case-insensitive), otherwise contains
        idx = next((i for i, t in enumerate(texts) if t.lower() == wanted.lower()), None)
        if idx is None:
            idx = next((i for i, t in enumerate(texts) if wanted.lower() in t.lower()), None)

        if idx is None:
            print(f"Warning: Option '{wanted}' not found among {texts[:10]}{'...' if len(texts)>10 else ''}")
            return

        # select by value if present, else by index
        val = await options.nth(idx).get_attribute("value")
        if val is not None and val.strip():
            await select.select_option(value=val)
        else:
            await select.select_option(index=idx)
    except Exception as e:
        print(f"Error selecting option '{wanted}': {e}")

async def fill_overlay_row(page, select_id: str, label_text: str, param_value: str):
    """Fill an overlay row with better error handling and multiple strategies."""
    try:
        # Wait for the select element
        select = page.locator(f"#{select_id}")
        await select.wait_for(state="visible", timeout=10000)

        # Select the overlay type
        await select_option_by_text(select, label_text)
        await page.wait_for_timeout(500)  # Small delay for UI updates

        # Find the parameter input field - for StockCharts v3, look for nearby number inputs
        param_input = None

        # Strategy 1: Look for a number input near this overlay menu
        # The pattern seems to be that parameter inputs are near the overlay selects
        overlay_number = select_id.split('-')[-1]  # Get the number (4, 5, 6)
        param_selectors = [
            f"input[type='number'][id*='{overlay_number}']",
            f"input[type='text'][id*='{overlay_number}']",
            f"#overlay-param-{overlay_number}",
            f"#param-{overlay_number}"
        ]

        for selector in param_selectors:
            try:
                candidate = page.locator(selector)
                if await candidate.count() > 0:
                    param_input = candidate.first
                    break
            except Exception:
                continue

        # Strategy 2: Look for input in the same container/row
        if param_input is None:
            try:
                container = select.locator("xpath=ancestor::div[1]")
                param_candidate = container.locator("input[type='number'], input[type='text']").first
                if await param_candidate.count() > 0:
                    param_input = param_candidate
            except Exception:
                pass

        # Strategy 3: Look for the next input after the select
        if param_input is None:
            try:
                param_candidate = select.locator("xpath=following::input[@type='number' or @type='text'][1]")
                if await param_candidate.count() > 0:
                    param_input = param_candidate
            except Exception:
                pass

        if param_input is not None:
            await param_input.wait_for(state="visible", timeout=5000)
            await param_input.clear()
            await param_input.fill(str(param_value))
            print(f"Set {label_text} parameter to {param_value}")
        else:
            print(f"Warning: Could not find parameter input for {label_text} (overlay {select_id})")

    except Exception as e:
        print(f"Error filling overlay row for {label_text}: {e}")

async def try_extract_id_from_img(page) -> Optional[str]:
    """Extract chart ID from various sources."""
    print("Trying to extract chart ID from page elements...")

    # Strategy 1: Look for chart images with various selectors
    img_selectors = [
        "img[src*='/c-sc/sc?']",
        "img[src*='stockcharts.com/c-sc/sc']",
        "img[src*='i=']",
        "#chart img",
        ".chart img",
        "img[id*='chart']",
        "canvas[id*='chart']",
        "svg[id*='chart']"
    ]

    for selector in img_selectors:
        try:
            print(f"Trying selector: {selector}")
            elements = page.locator(selector)
            count = await elements.count()
            print(f"Found {count} elements for {selector}")

            for i in range(count):
                element = elements.nth(i)
                src = await element.get_attribute("src")
                if src:
                    print(f"Found image src: {src}")
                    m = ID_REGEX.search(src)
                    if m:
                        chart_id = m.group(1)
                        print(f"✓ Extracted chart ID from img src: {chart_id}")
                        return chart_id
        except Exception as e:
            print(f"Error with selector {selector}: {e}")

    # Strategy 2: Look in background images and inline styles
    try:
        print("Checking background images and styles...")
        styled_elements = page.locator("[style*='/c-sc/sc?'], [style*='stockcharts.com'], [style*='i=']")
        count = await styled_elements.count()
        print(f"Found {count} styled elements")

        for i in range(count):
            style = await styled_elements.nth(i).get_attribute("style")
            if style:
                print(f"Found style: {style}")
                m = ID_REGEX.search(style)
                if m:
                    chart_id = m.group(1)
                    print(f"✓ Extracted chart ID from style: {chart_id}")
                    return chart_id
    except Exception as e:
        print(f"Error checking styles: {e}")

    # Strategy 3: Check if there are any data attributes with the ID
    try:
        print("Checking data attributes...")
        data_elements = page.locator("[data-chart-id], [data-id*='t'], [data-src*='i=']")
        count = await data_elements.count()
        print(f"Found {count} data elements")

        for i in range(count):
            element = data_elements.nth(i)
            for attr in ['data-chart-id', 'data-id', 'data-src']:
                value = await element.get_attribute(attr)
                if value:
                    print(f"Found {attr}: {value}")
                    m = ID_REGEX.search(value)
                    if m:
                        chart_id = m.group(1)
                        print(f"✓ Extracted chart ID from {attr}: {chart_id}")
                        return chart_id
    except Exception as e:
        print(f"Error checking data attributes: {e}")

    # Strategy 4: Look for the ID in page content/scripts
    try:
        print("Checking page content for chart ID...")
        page_content = await page.content()
        matches = ID_REGEX.findall(page_content)
        if matches:
            # Look for the most likely chart ID pattern (usually starts with 't')
            chart_matches = [m for m in matches if m.startswith('t') and len(m) > 8]
            if chart_matches:
                chart_id = chart_matches[0]  # Take the first one
                print(f"✓ Extracted chart ID from page content: {chart_id}")
                return chart_id
            elif matches:
                chart_id = matches[0]
                print(f"✓ Extracted chart ID from page content (fallback): {chart_id}")
                return chart_id
    except Exception as e:
        print(f"Error checking page content: {e}")

    print("Could not extract chart ID from any source")
    return None

async def get_chart_id(settings: Settings) -> str:
    async with async_playwright() as p:
        # Use more realistic browser settings
        browser = await p.chromium.launch(
            headless=settings.headless,
            slow_mo=settings.slow_mo,
            args=['--disable-blink-features=AutomationControlled']  # Try to avoid detection
        )

        ctx = await browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await ctx.new_page()
        page.set_default_timeout(settings.timeout_ms)
        page.set_default_navigation_timeout(settings.timeout_ms)

        found_id: Optional[str] = None

        def sniff(response):
            nonlocal found_id
            if found_id is None:
                url = response.url
                print(f"Network request: {url}")  # Debug: show all requests

                if IMG_URL_SUBSTR in url or 'stockcharts.com/c-sc/sc' in url:
                    print(f"Found chart image request: {url}")
                    m = ID_REGEX.search(url)
                    if m:
                        found_id = m.group(1)
                        print(f"✓ Extracted chart ID from network: {found_id}")

        page.on("response", sniff)

        print(f"Navigating to StockCharts for symbol: {settings.symbol}")
        # Build URL with the correct symbol
        start_url = f"https://stockcharts.com/sc3/ui/?s={settings.symbol}"
        await page.goto(start_url, wait_until="domcontentloaded")

        # Wait a bit for the page to fully load
        await page.wait_for_timeout(2000)

        try:
            # The symbol is already in the URL, so we may not need to set it
            # But let's try to update it if needed
            print(f"Checking if symbol needs to be updated to {settings.symbol}")

            # 1) Chart Type -> Candlesticks (using correct ID from the page structure)
            print("Setting chart type to Candlesticks")
            try:
                chart_select = page.locator("#chart-type-menu-lower")
                await chart_select.wait_for(state="visible", timeout=10000)
                await select_option_by_text(chart_select, "Candlesticks")
            except Exception as e:
                print(f"Could not set chart type: {e}")

            # 2) Overlays - using the correct IDs from the page structure
            print("Setting up overlays")
            overlay_configs = [
                ("overlay-menu-4", "Exp. Moving Avg", settings.ema),
                ("overlay-menu-5", "Simple Moving Avg", settings.sma1),
                ("overlay-menu-6", "Simple Moving Avg", settings.sma2)
            ]

            for select_id, label, value in overlay_configs:
                await fill_overlay_row(page, select_id, label, value)

            # 3) Set up volume indicator and clear others
            print("Setting up volume indicator and clearing others")
            try:
                # Set the first indicator to Volume specifically
                indicator1 = page.locator("#indicator-menu-1")
                if await indicator1.count() > 0:
                    await select_option_by_text(indicator1, "Volume")
                    print("Set indicator-1 to Volume")

                    # Set position to "Behind Price" for volume
                    position1 = page.locator("#position-menu-1")
                    if await position1.count() > 0:
                        await select_option_by_text(position1, "Behind Price")
                        print("Set volume position to Behind Price")

                # Clear other indicators
                other_indicators = [
                    "indicator-menu-2",
                    "indicator-menu-3",
                    "indicator-menu-15"
                ]

                for indicator_id in other_indicators:
                    try:
                        indicator_select = page.locator(f"#{indicator_id}")
                        if await indicator_select.count() > 0:
                            await select_option_by_text(indicator_select, "- None -")
                            print(f"Cleared indicator: {indicator_id}")
                    except Exception as e:
                        print(f"Could not clear indicator {indicator_id}: {e}")

            except Exception as e:
                print(f"Error configuring indicators: {e}")

            # 3) Update Chart - look for update/refresh buttons
            print("Looking for update/refresh button")
            update_clicked = False

            # Try different button selectors - StockCharts v3 might use different buttons
            update_selectors = [
                "button:has-text('Update')",
                "button:has-text('Refresh')",
                "button:has-text('Generate')",
                "input[type='submit']",
                "button[type='submit']",
                ".update-button",
                ".refresh-button",
                "#update-chart",
                "#refresh-chart"
            ]

            for selector in update_selectors:
                try:
                    button = page.locator(selector).first
                    if await button.count() > 0:
                        await button.wait_for(state="visible", timeout=5000)
                        await button.click()
                        update_clicked = True
                        print(f"Clicked update button using selector: {selector}")
                        break
                except Exception as e:
                    print(f"Update button selector {selector} failed: {e}")
                    continue

            # Alternative: The chart might auto-update when settings change
            if not update_clicked:
                print("No explicit update button found - chart may auto-update")
                await page.wait_for_timeout(2000)  # Give time for auto-update

            # 4) Wait for chart to load
            print("Waiting for chart to load...")
            await page.wait_for_timeout(3000)

            # 5) Find and right-click the chart image to get the direct URL
            print("Looking for chart image to right-click...")
            chart_found = False
            chart_image_selectors = [
                "img[src*='stockcharts.com']",
                "img[src*='/c-sc/']",
                "#chart img",
                ".chart img",
                "img[alt*='chart' i]",
                "img[title*='chart' i]",
                "canvas",
                "img:not([src*='logo']):not([src*='icon']):not([src*='button'])"  # Exclude obvious non-chart images
            ]

            chart_img = None
            for selector in chart_image_selectors:
                try:
                    elements = page.locator(selector)
                    count = await elements.count()
                    print(f"Found {count} elements for selector: {selector}")

                    if count > 0:
                        # Try each element to see if it looks like a chart
                        for i in range(count):
                            element = elements.nth(i)

                            # Check if it has a reasonable size (charts are usually large)
                            try:
                                bbox = await element.bounding_box()
                                if bbox and bbox['width'] > 200 and bbox['height'] > 150:
                                    print(f"Found potential chart image: {selector} (size: {bbox['width']}x{bbox['height']})")
                                    chart_img = element
                                    chart_found = True
                                    break
                            except Exception:
                                continue

                        if chart_found:
                            break

                except Exception as e:
                    print(f"Error checking selector {selector}: {e}")
                    continue

            if chart_img:
                try:
                    print("Right-clicking chart image to open context menu...")

                    # Right-click the chart image
                    await chart_img.click(button="right")
                    await page.wait_for_timeout(500)

                    # Look for "Open image in new tab" or similar option in context menu
                    context_menu_selectors = [
                        "text='Open image in new tab'",
                        "text='View image'",
                        "text='Open link in new tab'",
                        "[role='menuitem']:has-text('image')",
                        "[role='menuitem']:has-text('tab')",
                        ".context-menu-item:has-text('image')"
                    ]

                    context_clicked = False
                    for menu_selector in context_menu_selectors:
                        try:
                            menu_item = page.locator(menu_selector).first
                            if await menu_item.count() > 0:
                                print(f"Clicking context menu item: {menu_selector}")

                                # Listen for new page/tab creation
                                async with ctx.expect_page() as new_page_info:
                                    await menu_item.click()

                                new_page = await new_page_info.value
                                await new_page.wait_for_load_state("networkidle")

                                # Get the URL of the new tab (this should be the direct image URL)
                                image_url = new_page.url
                                print(f"New tab URL: {image_url}")

                                # Extract chart ID from this URL
                                m = ID_REGEX.search(image_url)
                                if m:
                                    found_id = m.group(1)
                                    print(f"✓ Extracted chart ID from new tab: {found_id}")
                                    context_clicked = True
                                    await new_page.close()
                                    break
                                else:
                                    print("No chart ID found in new tab URL")
                                    await new_page.close()

                        except Exception as e:
                            print(f"Failed to use context menu {menu_selector}: {e}")
                            continue

                    if not context_clicked:
                        print("Could not find or click context menu option")

                        # Alternative: Try to get the image src directly and construct the URL
                        try:
                            src = await chart_img.get_attribute("src")
                            if src:
                                print(f"Direct image src: {src}")
                                # Sometimes the src might be relative, construct full URL
                                if src.startswith('/'):
                                    full_url = f"https://stockcharts.com{src}"
                                else:
                                    full_url = src

                                m = ID_REGEX.search(full_url)
                                if m:
                                    found_id = m.group(1)
                                    print(f"✓ Extracted chart ID from img src: {found_id}")
                        except Exception as e:
                            print(f"Could not get image src: {e}")

                except Exception as e:
                    print(f"Error during right-click process: {e}")
            else:
                print("Could not find chart image to right-click")
                # Fallback to previous extraction methods
                extracted_id = await try_extract_id_from_img(page)
                if extracted_id:
                    found_id = extracted_id

        except Exception as e:
            print(f"Error during chart generation: {e}")
            # Take a screenshot for debugging if not headless
            if not settings.headless:
                await page.screenshot(path="debug_screenshot.png")

        finally:
            await browser.close()

        if not found_id:
            raise RuntimeError(
                "Could not capture the chart image ID. Possible issues:\n"
                "1. Page structure may have changed\n"
                "2. Symbol might be invalid\n"
                "3. Site may be detecting automation\n"
                "4. Network/loading issues\n"
                "Try running with --headed flag to debug visually."
            )

        print(f"Successfully extracted chart ID: {found_id}")
        return found_id

if __name__ == "__main__":
    s = parse_args(sys.argv)
    print(f"Settings: {s}")
    try:
        result = asyncio.run(get_chart_id(s))
        print(f"Chart ID: {result}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
