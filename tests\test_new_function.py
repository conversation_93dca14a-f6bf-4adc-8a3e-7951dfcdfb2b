#!/usr/bin/env python3
"""
Test the new get_etf_profile_data function
"""

import sys
sys.path.append('src')

from util.scrapper import get_etf_profile_data
import json

def test_function():
    """Test the new function"""
    
    tickers = ["SPY", "ROBT", "QQQ"]
    
    for ticker in tickers:
        print(f"\n=== {ticker} ===")
        result = get_etf_profile_data(ticker)
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    test_function()
