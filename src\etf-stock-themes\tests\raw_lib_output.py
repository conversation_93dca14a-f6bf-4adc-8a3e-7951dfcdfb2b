#!/usr/bin/env python3
"""
Raw output from pyetfdb-scraper library - no manipulation
"""

from pyetfdb_scraper.etf import ETF
import json

def show_raw_data(ticker):
    """Show exactly what the library returns"""

    print(f"=== RAW DATA FOR {ticker} ===")

    etf = ETF(ticker)

    # Show the to_dict() output
    print("to_dict() output:")
    data = etf.to_dict()
    print(json.dumps(data, indent=2))

    return data

if __name__ == "__main__":
    # Just show raw data for SPY
    raw_data = show_raw_data("ROBT")
