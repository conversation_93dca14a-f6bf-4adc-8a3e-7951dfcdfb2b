import sys
import json
import pandas as pd
from collections import defaultdict

# Simulate 1000 stocks data structure
def simulate_data_structures():
    # Simulate realistic data
    stocks = [f"STOCK{i:04d}" for i in range(1000)]
    niches = ["Big Tech", "Robotics & AI", "Internet", "Renewable Energy", "Consumer",
              "Mobility", "Nuclear Energy", "Cybersecurity", "Infrastructure", "Environment"]

    print("=== Memory Usage Analysis (1000 stocks) ===")

    # Option 1: Denormalized DataFrame (loaded in memory)
    denorm_data = []
    for i, stock in enumerate(stocks):
        # Each stock has ~6 ETF combinations, ~4 unique niches
        for j in range(6):
            denorm_data.append({
                'stock_ticker': stock,
                'ticker': f'ETF{i*6+j:04d}',
                'etf_name': f'ETF Name {i*6+j:04d}' * 3,  # Longer names
                'factset_niche': niches[j % len(niches)],
                'category': 'Technology Equities',
                'factset_segment': 'Equity: U.S. Big Tech'
            })

    df = pd.DataFrame(denorm_data)
    denorm_memory = sys.getsizeof(df) + df.memory_usage(deep=True).sum()
    print(f"Denormalized DataFrame: {denorm_memory:,} bytes ({denorm_memory/1024/1024:.1f} MB)")

    # Option 2: Stock-Niche Dictionary (optimized for our query)
    stock_niches = {}
    for stock in stocks:
        # Each stock has ~4 unique niches
        stock_niches[stock] = niches[:4]

    stock_niche_memory = sys.getsizeof(stock_niches)
    for k, v in stock_niches.items():
        stock_niche_memory += sys.getsizeof(k) + sys.getsizeof(v)
        for niche in v:
            stock_niche_memory += sys.getsizeof(niche)

    print(f"Stock-Niche Dictionary: {stock_niche_memory:,} bytes ({stock_niche_memory/1024/1024:.1f} MB)")

    # Option 3: Full normalized structure
    etf_details = {}
    stock_etf_mappings = defaultdict(list)

    for i, stock in enumerate(stocks):
        for j in range(6):
            etf_ticker = f'ETF{i*6+j:04d}'
            etf_details[etf_ticker] = {
                'etf_name': f'ETF Name {i*6+j:04d}' * 3,
                'factset_niche': niches[j % len(niches)],
                'category': 'Technology Equities'
            }
            stock_etf_mappings[stock].append(etf_ticker)

    etf_memory = sys.getsizeof(etf_details)
    mapping_memory = sys.getsizeof(stock_etf_mappings)

    print(f"ETF Details Dict: {etf_memory:,} bytes ({etf_memory/1024/1024:.1f} MB)")
    print(f"Stock-ETF Mappings: {mapping_memory:,} bytes ({mapping_memory/1024/1024:.1f} MB)")
    print(f"Total Normalized: {(etf_memory + mapping_memory):,} bytes ({(etf_memory + mapping_memory)/1024/1024:.1f} MB)")

    # Performance comparison
    print(f"\n=== Query Performance Simulation ===")

    # Test query: get niches for a stock
    test_stock = "STOCK0500"

    # Method 1: DataFrame query
    import time
    start = time.time()
    df_niches = df[df['stock_ticker'] == test_stock]['factset_niche'].unique().tolist()
    df_time = time.time() - start

    # Method 2: Dictionary lookup
    start = time.time()
    dict_niches = stock_niches.get(test_stock, [])
    dict_time = time.time() - start

    print(f"DataFrame query: {df_time*1000:.3f}ms")
    print(f"Dictionary lookup: {dict_time*1000:.6f}ms")
    if dict_time > 0:
        print(f"Speedup: {df_time/dict_time:.0f}x faster")
    else:
        print(f"Dictionary lookup too fast to measure (>1000x faster)")

    return {
        'denorm_memory': denorm_memory,
        'stock_niche_memory': stock_niche_memory,
        'normalized_memory': etf_memory + mapping_memory,
        'df_time': df_time,
        'dict_time': dict_time
    }

if __name__ == "__main__":
    results = simulate_data_structures()
