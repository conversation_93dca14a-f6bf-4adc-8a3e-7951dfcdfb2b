#!/usr/bin/env python3
"""
Enhanced Theme ETF Collector
Handles one-to-many mappings, batch processing, ignore lists, and theme filtering
"""

import asyncio
import aiohttp
import pandas as pd
import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Set, Optional
import time

# Add src to path - adjust for new directory structure
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

try:
    from pyetfdb_scraper.etf import ETF
    PYETFDB_AVAILABLE = True
except ImportError:
    print("⚠️  pyetfdb-scraper not available. Install with: pip install pyetfdb-scraper")
    PYETFDB_AVAILABLE = False
    sys.exit(1)

from util.scrapper import etfHoldingsETFDBdotCOM

# Configuration - updated paths for new directory structure
DATA_DIR = os.path.join(src_dir, "data", "etf-data")
CSV_FILE = os.path.join(DATA_DIR, "theme_etfs.csv")
IGNORE_LIST_FILE = os.path.join(DATA_DIR, "etf_ignore_list.json")
PROCESSED_ETFS_FILE = os.path.join(DATA_DIR, "processed_etfs.json")
STOCK_NICHES_CACHE_FILE = os.path.join(DATA_DIR, "stock_niches_cache.json")

CSV_COLUMNS = [
    'stock_ticker',
    'ticker',
    'etf_name',
    'category',
    'factset_segment',
    'factset_category',
    'factset_focus',
    'factset_niche',
    'factset_strategy',
    'factset_weighting_scheme'
]

class EnhancedThemeETFCollector:
    """Enhanced ETF collector with caching, ignore list, and batch processing"""

    def __init__(self):
        self.existing_etfs: Set[str] = set()
        self.ignore_list: Set[str] = set()
        self.processed_etfs: Set[str] = set()  # Just processed tickers
        self.theme_etfs: Set[str] = set()      # Theme ETF tickers
        self.non_theme_etfs: Set[str] = set()  # Non-theme ETF tickers
        self.stock_to_etfs: Dict[str, List[str]] = {}

        # Hot cache for O(1) stock → niches queries
        self.stock_niches_cache: Dict[str, List[str]] = {}

        self.load_existing_data()
        self.load_stock_niches_cache()

    def load_stock_niches_cache(self):
        """Load precomputed stock → niches mapping for O(1) queries"""

        if os.path.exists(STOCK_NICHES_CACHE_FILE):
            try:
                with open(STOCK_NICHES_CACHE_FILE, 'r') as f:
                    self.stock_niches_cache = json.load(f)
                print(f"🚀 Loaded stock-niches cache: {len(self.stock_niches_cache)} stocks")
            except Exception as e:
                print(f"⚠️  Error loading stock-niches cache: {e}")
                self.stock_niches_cache = {}
        else:
            print("📝 No stock-niches cache found. Will build from CSV if needed.")
            self.stock_niches_cache = {}

    def rebuild_stock_niches_cache(self):
        """Rebuild stock → niches cache from CSV data"""

        if not os.path.exists(CSV_FILE):
            print("⚠️  No CSV file found. Cannot rebuild cache.")
            return

        try:
            df = pd.read_csv(CSV_FILE)
            if 'stock_ticker' not in df.columns or 'factset_niche' not in df.columns:
                print("⚠️  CSV missing required columns. Cannot rebuild cache.")
                return

            # Group by stock and get unique niches
            self.stock_niches_cache = df.groupby('stock_ticker')['factset_niche'].apply(
                lambda x: sorted(x.unique())
            ).to_dict()

            # Save cache to file
            with open(STOCK_NICHES_CACHE_FILE, 'w') as f:
                json.dump(self.stock_niches_cache, f, indent=2)

            print(f"✅ Rebuilt stock-niches cache: {len(self.stock_niches_cache)} stocks")

        except Exception as e:
            print(f"⚠️  Error rebuilding cache: {e}")

    def get_stock_niches(self, stock_ticker: str) -> List[str]:
        """Get unique factset_niches for a stock - O(1) optimized query"""

        # Try hot cache first (O(1))
        if stock_ticker in self.stock_niches_cache:
            return self.stock_niches_cache[stock_ticker]

        # Fallback to CSV query (O(n)) and update cache
        if os.path.exists(CSV_FILE):
            try:
                df = pd.read_csv(CSV_FILE)
                if 'stock_ticker' in df.columns and 'factset_niche' in df.columns:
                    niches = df[df['stock_ticker'] == stock_ticker]['factset_niche'].unique().tolist()
                    niches = sorted(niches)  # Keep consistent ordering

                    # Update cache for future queries
                    self.stock_niches_cache[stock_ticker] = niches
                    return niches
            except Exception as e:
                print(f"⚠️  Error querying CSV for {stock_ticker}: {e}")

        return []

    def load_existing_data(self):
        """Load existing ETFs, ignore list, and processed ETFs from files"""

        # Ensure data directory exists
        os.makedirs(DATA_DIR, exist_ok=True)

        # Load existing ETFs from CSV
        if os.path.exists(CSV_FILE):
            try:
                df = pd.read_csv(CSV_FILE)
                self.existing_etfs = set(df['ticker'].tolist())
                print(f"📁 Loaded {len(self.existing_etfs)} existing ETFs from CSV")
            except Exception as e:
                print(f"⚠️  Error loading CSV: {e}")

        # Load ignore list
        if os.path.exists(IGNORE_LIST_FILE):
            try:
                with open(IGNORE_LIST_FILE, 'r') as f:
                    ignore_data = json.load(f)
                    self.ignore_list = set(ignore_data.get('etfs', []))
                print(f"🚫 Loaded {len(self.ignore_list)} ETFs in ignore list")
            except Exception as e:
                print(f"⚠️  Error loading ignore list: {e}")

        # Load processed ETFs cache (minimal format)
        if os.path.exists(PROCESSED_ETFS_FILE):
            try:
                with open(PROCESSED_ETFS_FILE, 'r') as f:
                    cache_data = json.load(f)

                # Handle both old format (dict) and new format (minimal)
                if isinstance(cache_data, dict):
                    if 'processed_etfs' in cache_data:
                        # New minimal format
                        self.processed_etfs = set(cache_data.get('processed_etfs', []))
                        self.theme_etfs = set(cache_data.get('theme_etfs', []))
                        self.non_theme_etfs = set(cache_data.get('non_theme_etfs', []))
                    else:
                        # Old format - convert to new format
                        print("🔄 Converting old cache format to minimal format...")
                        for ticker, data in cache_data.items():
                            self.processed_etfs.add(ticker)
                            if 'Theme' in data.get('factset_focus', ''):
                                self.theme_etfs.add(ticker)
                            else:
                                self.non_theme_etfs.add(ticker)
                        # Save in new format
                        self.save_processed_etfs_cache()

                print(f"💾 Loaded {len(self.processed_etfs)} processed ETFs from cache")
                print(f"   Theme ETFs: {len(self.theme_etfs)}, Non-theme: {len(self.non_theme_etfs)}")
            except Exception as e:
                print(f"⚠️  Error loading processed ETFs cache: {e}")

    def save_ignore_list(self):
        """Save ignore list to file"""
        ignore_data = {
            'etfs': list(self.ignore_list),
            'last_updated': datetime.now().isoformat()
        }
        with open(IGNORE_LIST_FILE, 'w') as f:
            json.dump(ignore_data, f, indent=2)

    def save_processed_etfs_cache(self):
        """Save processed ETFs cache in minimal format"""
        try:
            cache_data = {
                'processed_etfs': list(self.processed_etfs),
                'theme_etfs': list(self.theme_etfs),
                'non_theme_etfs': list(self.non_theme_etfs),
                'last_updated': datetime.now().isoformat()
            }

            with open(PROCESSED_ETFS_FILE, 'w') as f:
                json.dump(cache_data, f, indent=2)

        except Exception as e:
            print(f"⚠️  Error saving processed ETFs cache: {e}")

    def save_processed_etfs(self):
        """Legacy method - calls new save method"""
        self.save_processed_etfs_cache()

    def get_etf_profile_data_cached(self, ticker: str) -> Optional[dict]:
        """Get ETF profile data with caching"""

        # Check if already processed
        if ticker in self.processed_etfs:
            # Return cached status without fetching data
            if ticker in self.theme_etfs:
                return {'is_theme': True, 'cached': True, 'ticker': ticker}
            else:
                return {'is_theme': False, 'cached': True, 'ticker': ticker}

        # Check if in ignore list
        if ticker in self.ignore_list:
            return None

        try:
            etf = ETF(ticker)
            data = etf.to_dict()

            # Start with basic fields
            result = {
                'ticker': ticker,
                'etf_name': data.get('info', {}).get('vitals', {}).get('etf_name', ''),
                'category': data.get('info', {}).get('dbtheme', {}).get('category', '')
            }

            # Flatten factset data
            factset = data.get('info', {}).get('fact_set', {})
            for key, value in factset.items():
                if isinstance(value, list):
                    result[f'factset_{key}'] = ', '.join(value) if value else ''
                else:
                    result[f'factset_{key}'] = value or ''

            # Update cache sets
            self.processed_etfs.add(ticker)
            if 'Theme' in result.get('factset_focus', ''):
                self.theme_etfs.add(ticker)
                result['is_theme'] = True
            else:
                self.non_theme_etfs.add(ticker)
                result['is_theme'] = False

            result['cached'] = False
            self.save_processed_etfs_cache()

            return result

        except Exception as e:
            print(f"❌ Error processing {ticker}: {e}")
            # Add to ignore list if it fails
            self.ignore_list.add(ticker)
            self.save_ignore_list()
            return None

    def batch_process_etfs(self, etf_tickers: List[str]) -> Dict[str, dict]:
        """Process multiple ETFs with caching and ignore list"""

        results = {}
        new_etfs = []

        # Filter out already processed and ignored ETFs
        for ticker in etf_tickers:
            if ticker not in self.processed_etfs and ticker not in self.ignore_list:
                new_etfs.append(ticker)

        print(f"🔄 Processing {len(new_etfs)} new ETFs (skipping {len(etf_tickers) - len(new_etfs)} already processed/ignored)")

        # Process new ETFs
        for i, ticker in enumerate(new_etfs):
            print(f"  Processing {ticker} ({i+1}/{len(new_etfs)})...")

            result = self.get_etf_profile_data_cached(ticker)
            if result:
                results[ticker] = result

            # Small delay to be respectful
            if i < len(new_etfs) - 1:
                time.sleep(0.5)

        # Add already processed ETFs to results
        for ticker in etf_tickers:
            if ticker in self.processed_etfs:
                # For cached theme ETFs that are NOT in CSV, we need full data
                if ticker in self.theme_etfs and ticker not in self.existing_etfs:
                    # Need to fetch data for theme ETFs not yet in CSV
                    try:
                        from util.scrapper import get_etf_profile_data
                        data = get_etf_profile_data(ticker)
                        data['is_theme'] = True
                        data['cached'] = True
                        results[ticker] = data
                        print(f"  Fetching data for cached theme ETF: {ticker}")
                    except:
                        results[ticker] = {'is_theme': True, 'cached': True, 'ticker': ticker}
                else:
                    # Just return cached status for ETFs already in CSV or non-theme ETFs
                    if ticker in self.theme_etfs:
                        results[ticker] = {'is_theme': True, 'cached': True, 'ticker': ticker}
                    else:
                        results[ticker] = {'is_theme': False, 'cached': True, 'ticker': ticker}

        # Save caches
        self.save_processed_etfs_cache()
        self.save_ignore_list()

        return results

    def filter_theme_etfs(self, etf_data: Dict[str, dict]) -> Dict[str, dict]:
        """Filter ETFs where factset_focus contains 'Theme'"""

        theme_etfs = {}

        for ticker, data in etf_data.items():
            # Check if it's marked as theme ETF or has Theme in factset_focus
            if data.get('is_theme', False) or 'Theme' in data.get('factset_focus', ''):
                theme_etfs[ticker] = data

        return theme_etfs

    def save_etf_to_csv(self, etf_data: dict, stock_ticker: str):
        """Save ETF data to CSV with stock ticker mapping"""

        # Prepare row data
        row_data = {'stock_ticker': stock_ticker}
        for col in CSV_COLUMNS[1:]:  # Skip stock_ticker as it's already added
            row_data[col] = etf_data.get(col, '')

        # Create DataFrame
        df_new = pd.DataFrame([row_data])

        # Append to CSV
        if os.path.exists(CSV_FILE):
            df_new.to_csv(CSV_FILE, mode='a', header=False, index=False)
        else:
            df_new.to_csv(CSV_FILE, mode='w', header=True, index=False)

        # Update stock-niches cache for this stock
        self.update_stock_niches_cache(stock_ticker, etf_data.get('factset_niche', ''))

    def update_stock_niches_cache(self, stock_ticker: str, new_niche: str):
        """Update the stock-niches cache when new data is added"""

        if not new_niche:
            return

        # Get current niches for this stock
        current_niches = self.stock_niches_cache.get(stock_ticker, [])

        # Add new niche if not already present
        if new_niche not in current_niches:
            current_niches.append(new_niche)
            current_niches.sort()  # Keep consistent ordering
            self.stock_niches_cache[stock_ticker] = current_niches

            # Save updated cache to file
            try:
                with open(STOCK_NICHES_CACHE_FILE, 'w') as f:
                    json.dump(self.stock_niches_cache, f, indent=2)
            except Exception as e:
                print(f"⚠️  Error saving updated cache: {e}")

    def get_cache_stats(self) -> Dict[str, any]:
        """Get statistics about the stock-niches cache"""

        if not self.stock_niches_cache:
            return {
                'total_stocks': 0,
                'total_unique_niches': 0,
                'total_stock_niche_combinations': 0,
                'avg_niches_per_stock': 0,
                'cache_file_exists': os.path.exists(STOCK_NICHES_CACHE_FILE),
                'unique_niches': []
            }

        all_niches = set()
        total_niche_count = 0

        for _, niches in self.stock_niches_cache.items():
            all_niches.update(niches)
            total_niche_count += len(niches)

        return {
            'total_stocks': len(self.stock_niches_cache),
            'total_unique_niches': len(all_niches),
            'total_stock_niche_combinations': total_niche_count,
            'avg_niches_per_stock': total_niche_count / len(self.stock_niches_cache),
            'cache_file_exists': os.path.exists(STOCK_NICHES_CACHE_FILE),
            'unique_niches': sorted(list(all_niches))
        }

    def print_cache_stats(self):
        """Print cache statistics in a readable format"""

        stats = self.get_cache_stats()

        print(f"\n📊 Stock-Niches Cache Statistics:")
        print(f"   Total stocks: {stats['total_stocks']}")
        print(f"   Unique niches: {stats['total_unique_niches']}")
        print(f"   Avg niches per stock: {stats['avg_niches_per_stock']:.1f}")
        print(f"   Cache file exists: {stats['cache_file_exists']}")

        if stats['total_stocks'] > 0:
            print(f"   Available niches: {', '.join(stats['unique_niches'][:5])}{'...' if len(stats['unique_niches']) > 5 else ''}")

    def update_stock_to_etf_mapping(self, stock_ticker: str, etf_tickers: List[str]):
        """Update the stock-to-ETF mapping for one-to-many relationships"""

        if stock_ticker not in self.stock_to_etfs:
            self.stock_to_etfs[stock_ticker] = []

        # Add new ETFs to the mapping
        for etf_ticker in etf_tickers:
            if etf_ticker not in self.stock_to_etfs[stock_ticker]:
                self.stock_to_etfs[stock_ticker].append(etf_ticker)

    def has_stock_been_processed(self, stock_ticker: str) -> bool:
        """Check if a stock has already been processed by looking at CSV"""

        # Check if stock exists in CSV
        if os.path.exists(CSV_FILE):
            try:
                df = pd.read_csv(CSV_FILE)
                if 'stock_ticker' in df.columns:
                    exists = stock_ticker in df['stock_ticker'].values
                    return exists
            except Exception as e:
                print(f"⚠️  Error checking CSV for stock {stock_ticker}: {e}")

        return False

    def count_theme_etfs_for_stock(self, stock_ticker: str) -> int:
        """Count how many theme ETFs exist for a stock in the CSV"""

        if os.path.exists(CSV_FILE):
            try:
                df = pd.read_csv(CSV_FILE)
                if 'stock_ticker' in df.columns:
                    stock_etfs = df[df['stock_ticker'] == stock_ticker]
                    return len(stock_etfs)
            except Exception as e:
                print(f"⚠️  Error counting ETFs for stock {stock_ticker}: {e}")

        return 0

    def stock_etf_combination_exists(self, stock_ticker: str, etf_ticker: str) -> bool:
        """Check if a specific stock-ETF combination already exists in CSV"""

        if os.path.exists(CSV_FILE):
            try:
                df = pd.read_csv(CSV_FILE)
                if 'stock_ticker' in df.columns and 'ticker' in df.columns:
                    combination_exists = ((df['stock_ticker'] == stock_ticker) &
                                        (df['ticker'] == etf_ticker)).any()
                    return combination_exists
            except Exception as e:
                print(f"⚠️  Error checking combination {stock_ticker}-{etf_ticker}: {e}")

        return False

    async def process_stock(self, stock_ticker: str) -> dict:
        """Process a single stock and find theme ETFs"""

        print(f"\n=== Processing {stock_ticker} ===")

        # Check if we've already processed this stock
        if self.has_stock_been_processed(stock_ticker):
            print(f"⏭️  {stock_ticker} already processed - skipping ETF holdings fetch")
            theme_count = self.count_theme_etfs_for_stock(stock_ticker)
            result = {
                'stock_ticker': stock_ticker,
                'etfs_found': theme_count,  # For already processed, we only care about theme ETFs
                'theme_etfs_found': theme_count,
                'new_etfs_added': 0,
                'already_existed': theme_count
            }
            return result

        result = {
            'stock_ticker': stock_ticker,
            'etfs_found': 0,
            'theme_etfs_found': 0,
            'new_etfs_added': 0,
            'already_existed': 0
        }

        async with aiohttp.ClientSession() as session:
            # Step 1: Get ETFs holding this stock
            try:
                etf_holdings_df = await etfHoldingsETFDBdotCOM(session, stock_ticker)

                if etf_holdings_df.empty:
                    print(f"❌ No ETFs found holding {stock_ticker}")
                    return result

                # Extract ETF tickers from the 'Ticker' column
                etf_tickers = []
                if 'Ticker' in etf_holdings_df.columns:
                    etf_tickers = etf_holdings_df['Ticker'].tolist()
                else:
                    print(f"⚠️  Could not find Ticker column. Columns: {etf_holdings_df.columns.tolist()}")
                    return result

                result['etfs_found'] = len(etf_tickers)
                print(f"📊 Found {len(etf_tickers)} ETFs holding {stock_ticker}")

                # Update stock-to-ETF mapping
                self.update_stock_to_etf_mapping(stock_ticker, etf_tickers)

            except Exception as e:
                print(f"❌ Error getting ETF holdings for {stock_ticker}: {e}")
                return result

        # Step 2: Batch process ETFs
        etf_data = self.batch_process_etfs(etf_tickers)

        # Step 3: Filter for theme ETFs
        theme_etfs = self.filter_theme_etfs(etf_data)
        result['theme_etfs_found'] = len(theme_etfs)

        print(f"🎯 Found {len(theme_etfs)} theme ETFs")

        # Step 4: Save theme ETFs to CSV (one-to-many mapping)
        for ticker, data in theme_etfs.items():
            # Check if this specific stock-ETF combination exists
            if not self.stock_etf_combination_exists(stock_ticker, ticker):
                # If data is minimal (cached), fetch full data for CSV
                if data.get('cached', False) and len(data) <= 4:  # Minimal cached data
                    try:
                        from util.scrapper import get_etf_profile_data
                        full_data = get_etf_profile_data(ticker)
                        full_data['is_theme'] = True
                        full_data['cached'] = True
                        print(f"  🔄 Fetching full data for cached theme ETF: {ticker}")
                        self.save_etf_to_csv(full_data, stock_ticker)
                    except Exception as e:
                        print(f"  ⚠️  Error fetching full data for {ticker}: {e}")
                        self.save_etf_to_csv(data, stock_ticker)  # Save minimal data as fallback
                else:
                    # Data is already complete
                    self.save_etf_to_csv(data, stock_ticker)

                result['new_etfs_added'] += 1
                niche = data.get('factset_niche', 'Unknown')
                print(f"  ✅ {ticker}: Added for {stock_ticker} - {niche}")
            else:
                result['already_existed'] += 1
                print(f"  ⏭️  {ticker}: Already exists for {stock_ticker}")

            # Always update the existing ETFs set for tracking
            self.existing_etfs.add(ticker)

        return result

    async def process_multiple_stocks(self, stock_tickers: List[str]) -> dict:
        """Process multiple stocks"""

        print("Enhanced Theme ETF Collector")
        print("=" * 60)

        total_results = {
            'stocks_processed': 0,
            'total_etfs_found': 0,
            'total_theme_etfs_found': 0,
            'total_new_etfs_added': 0,
            'stock_results': {}
        }

        for stock_ticker in stock_tickers:
            result = await self.process_stock(stock_ticker)

            total_results['stocks_processed'] += 1
            total_results['total_etfs_found'] += result['etfs_found']
            total_results['total_theme_etfs_found'] += result['theme_etfs_found']
            total_results['total_new_etfs_added'] += result['new_etfs_added']
            total_results['stock_results'][stock_ticker] = result

            print(f"\n{stock_ticker} Summary:")
            print(f"  ETFs found: {result['etfs_found']}")
            print(f"  Theme ETFs: {result['theme_etfs_found']}")
            print(f"  New ETFs added: {result['new_etfs_added']}")
            print(f"  Already existed: {result['already_existed']}")

        return total_results

    def display_final_summary(self, results: dict):
        """Display final summary"""

        print(f"\n{'='*60}")
        print("FINAL SUMMARY")
        print(f"{'='*60}")
        print(f"Stocks processed: {results['stocks_processed']}")
        print(f"Total ETFs found: {results['total_etfs_found']}")
        print(f"Total theme ETFs found: {results['total_theme_etfs_found']}")
        print(f"New theme ETFs added to CSV: {results['total_new_etfs_added']}")
        print(f"Total ETFs in CSV: {len(self.existing_etfs)}")
        print(f"ETFs in ignore list: {len(self.ignore_list)}")
        print(f"ETFs in cache: {len(self.processed_etfs)}")
        print(f"  Theme ETFs cached: {len(self.theme_etfs)}")
        print(f"  Non-theme ETFs cached: {len(self.non_theme_etfs)}")

        # Show file locations
        print(f"\nData Files:")
        print(f"  CSV: {CSV_FILE}")
        print(f"  Ignore list: {IGNORE_LIST_FILE}")
        print(f"  Cache: {PROCESSED_ETFS_FILE} (minimal format)")

        # Show stock-to-ETF mapping summary
        print(f"\nStock-to-ETF Mapping:")
        for stock, etfs in self.stock_to_etfs.items():
            print(f"  {stock}: {len(etfs)} ETFs")

async def main():
    """Main function"""

    if not PYETFDB_AVAILABLE:
        print("Please install pyetfdb-scraper: pip install pyetfdb-scraper")
        return

    # Test stocks - you can modify this list
    test_stocks = ['LYFT', 'AAPL']  # Mix: processed + new stock

    collector = EnhancedThemeETFCollector()
    results = await collector.process_multiple_stocks(test_stocks)
    collector.display_final_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
