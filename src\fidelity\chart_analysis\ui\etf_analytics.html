<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>ETF Theme Analytics</title>
  <link rel="stylesheet" href="/ui/styles.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .analytics-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .analytics-header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 10px;
    }

    .analytics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }

    .analytics-card {
      background: white;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
    }

    .analytics-card h3 {
      margin: 0 0 15px 0;
      color: #374151;
      font-size: 1.2em;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 10px;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }

    .stat-item {
      text-align: center;
      padding: 15px;
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }

    .stat-value {
      font-size: 2em;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9em;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .chart-container {
      position: relative;
      height: 400px;
      margin: 20px 0;
    }

    .niches-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }

    .niches-table th,
    .niches-table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    .niches-table th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
      font-size: 0.9em;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .niches-table tr:hover {
      background: #f9fafb;
      cursor: pointer;
    }

    .strength-bar {
      width: 100%;
      height: 20px;
      background: #e5e7eb;
      border-radius: 10px;
      overflow: hidden;
      position: relative;
    }

    .strength-fill {
      height: 100%;
      background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
      border-radius: 10px;
      transition: width 0.3s ease;
    }

    .strength-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 0.8em;
      font-weight: 600;
      color: white;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .rs-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      font-weight: 600;
      color: white;
    }

    .rs-exceptional { background: #10b981; }
    .rs-strong { background: #3b82f6; }
    .rs-moderate { background: #f59e0b; }
    .rs-weak { background: #ef4444; }

    .back-button {
      position: fixed;
      top: 20px;
      left: 20px;
      background: #374151;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 0.9em;
      z-index: 1000;
    }

    .back-button:hover {
      background: #1f2937;
    }

    .loading {
      text-align: center;
      padding: 50px;
      color: #6b7280;
    }

    .error {
      text-align: center;
      padding: 50px;
      color: #ef4444;
      background: #fef2f2;
      border: 1px solid #fecaca;
      border-radius: 8px;
      margin: 20px;
    }

    @media (max-width: 768px) {
      .analytics-grid {
        grid-template-columns: 1fr;
      }

      .summary-stats {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <button class="back-button" onclick="goBack()">← Back to Screener</button>

  <div class="analytics-container">
    <div class="analytics-header">
      <h1>🎯 ETF Theme Analytics</h1>
      <p>Distribution and strength analysis of stock themes for your current screener results</p>
      <div id="screener-info" style="margin-top: 10px; font-size: 0.9em; opacity: 0.9;">
        Loading screener information...
      </div>
    </div>

    <div id="loading" class="loading">
      <h3>Loading ETF analytics...</h3>
      <p>Analyzing theme distributions and RS ratings...</p>
    </div>

    <div id="error" class="error" style="display: none;">
      <h3>Error Loading Data</h3>
      <p id="error-message"></p>
    </div>

    <div id="content" style="display: none;">
      <div class="analytics-grid">
        <!-- Summary Statistics -->
        <div class="analytics-card">
          <h3>📊 Overview</h3>
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-value" id="total-stocks">-</div>
              <div class="stat-label">Total Stocks</div>
            </div>
            <div class="stat-item">
              <div class="stat-value" id="total-niches">-</div>
              <div class="stat-label">Theme Niches</div>
            </div>
            <div class="stat-item">
              <div class="stat-value" id="avg-niches">-</div>
              <div class="stat-label">Avg Niches/Stock</div>
            </div>
            <div class="stat-item">
              <div class="stat-value" id="top-niche-score">-</div>
              <div class="stat-label">Top Niche Score</div>
            </div>
          </div>
        </div>

        <!-- Pie Chart -->
        <div class="analytics-card">
          <h3>🥧 Theme Distribution</h3>
          <div class="chart-container">
            <canvas id="pieChart"></canvas>
          </div>
        </div>
      </div>

      <!-- Top Niches Table -->
      <div class="analytics-card">
        <h3>🏆 Top Performing Themes</h3>
        <p style="color: #6b7280; margin-bottom: 15px;">
          Strength score combines stock count (max 50 pts) and average RS rating quality (max 50 pts)
        </p>
        <table class="niches-table">
          <thead>
            <tr>
              <th>Theme Niche</th>
              <th>Stock Count</th>
              <th>Avg RS Rating</th>
              <th>High RS (≥70)</th>
              <th>Exceptional RS (≥90)</th>
              <th>Strength Score</th>
            </tr>
          </thead>
          <tbody id="niches-tbody">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    let pieChart = null;

    async function loadAnalytics() {
      try {
        const response = await fetch('/etf-analytics');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Hide loading, show content
        document.getElementById('loading').style.display = 'none';
        document.getElementById('content').style.display = 'block';

        // Update screener info
        const screenerInfo = document.getElementById('screener-info');
        if (data.screener_info) {
          screenerInfo.textContent = `${data.screener_info.stocks_with_themes} of ${data.screener_info.total_screener_stocks} screener stocks have theme data`;
        } else {
          screenerInfo.textContent = 'Analyzing current screener results';
        }

        // Update summary stats
        document.getElementById('total-stocks').textContent = data.summary.total_stocks;
        document.getElementById('total-niches').textContent = data.summary.total_niches;
        document.getElementById('avg-niches').textContent = data.summary.avg_niches_per_stock.toFixed(1);
        document.getElementById('top-niche-score').textContent = data.summary.top_niche_score.toFixed(0);

        // Create pie chart
        createPieChart(data.pie_chart);

        // Populate niches table
        populateNichesTable(data.top_niches);

      } catch (error) {
        console.error('Error loading analytics:', error);
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('error-message').textContent = error.message;
      }
    }

    function createPieChart(chartData) {
      const ctx = document.getElementById('pieChart').getContext('2d');

      // Generate colors based on strength scores
      const colors = chartData.scores.map(score => {
        const intensity = Math.min(score / 100, 1);
        const hue = intensity * 120; // 0 (red) to 120 (green)
        return `hsl(${hue}, 70%, 60%)`;
      });

      pieChart = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: chartData.labels,
          datasets: [{
            data: chartData.values,
            backgroundColor: colors,
            borderColor: '#ffffff',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                boxWidth: 12,
                padding: 15,
                font: {
                  size: 11
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label;
                  const value = context.parsed;
                  const score = chartData.scores[context.dataIndex];
                  return `${label}: ${value} stocks (Score: ${score.toFixed(0)})`;
                }
              }
            }
          }
        }
      });
    }

    function populateNichesTable(niches) {
      const tbody = document.getElementById('niches-tbody');
      tbody.innerHTML = '';

      niches.forEach(niche => {
        const row = document.createElement('tr');
        row.onclick = () => showNicheDetails(niche.niche);

        row.innerHTML = `
          <td style="font-weight: 600; color: #374151;">${niche.niche}</td>
          <td>${niche.stock_count}</td>
          <td>${niche.avg_rs_rating.toFixed(1)}</td>
          <td>
            <span class="rs-badge rs-strong">${niche.high_rs_count}</span>
          </td>
          <td>
            <span class="rs-badge rs-exceptional">${niche.exceptional_rs_count}</span>
          </td>
          <td>
            <div class="strength-bar">
              <div class="strength-fill" style="width: ${niche.strength_score}%"></div>
              <div class="strength-text">${niche.strength_score.toFixed(0)}</div>
            </div>
          </td>
        `;

        tbody.appendChild(row);
      });
    }

    function showNicheDetails(niche) {
      // TODO: Implement detailed niche view
      alert(`Detailed view for "${niche}" coming soon!\n\nThis will show:\n- Individual stocks in this niche\n- Their RS ratings\n- Performance metrics\n- Related ETFs`);
    }

    function goBack() {
      window.location.href = '/app';
    }

    // Load analytics when page loads
    document.addEventListener('DOMContentLoaded', loadAnalytics);
  </script>
</body>
</html>
