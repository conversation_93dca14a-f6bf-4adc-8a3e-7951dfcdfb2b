import os
import sys
import glob
import pandas as pd
import asyncio
import re
import time
from datetime import datetime

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from stockdata.data_source import getStockDataV3, getStockDataV3BatchChunked
from common.http_session import get_session
import aiohttp

def to_yahoo_symbol(sym: str) -> str:
    s = (sym or "").strip().upper().replace("/", "-")
    m = re.fullmatch(r"([A-Z0-9]+)\.([A-Z])", s)
    return f"{m.group(1)}-{m.group(2)}" if m else s

async def main():
    start_time = time.time()
    print(f"🔍 Daily Scanner Started - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    screener_dir = 'src/data/screener/'
    list_of_files = glob.glob(os.path.join(screener_dir, 'filtered_sorted_*.xlsx'))
    if not list_of_files:
        print("No screener files found.")
        return

    # Collect & normalize symbols from all sheets
    print("📁 Loading screener files...")
    all_symbols = set()
    for file in list_of_files:
        print(f"   Processing: {os.path.basename(file)}")
        df = pd.read_excel(file)
        # Robust column access
        col = next((c for c in df.columns if c.strip().lower() == "symbol"), None)
        if col is None:
            print(f"   ⚠️ Warning: no 'Symbol' column in {file}; skipping.")
            continue
        file_symbols = [to_yahoo_symbol(s) for s in df[col].astype(str).tolist()]
        all_symbols.update(file_symbols)
        print(f"   ✅ Added {len(file_symbols)} symbols from {os.path.basename(file)}")

    unique_symbols = sorted(all_symbols)
    print(f"\n📊 Found {len(unique_symbols)} unique symbols to scan.")
    print(f"⏱️ Setup completed in {time.time() - start_time:.2f} seconds")

    inside_day_only_symbols = []
    pocket_pivot_only_symbols = []
    ants_only_symbols = []
    both_symbols = []
    ants_and_inside_day_symbols = []
    ants_and_pocket_pivot_symbols = []
    all_three_symbols = []
    failed_symbols = []

    # Use batch processing for efficiency
    download_start = time.time()
    async with aiohttp.ClientSession() as session:
        print("\n🚀 Starting batch download of stock data...")

        # Use chunked batch processing (50 symbols per batch for optimal performance)
        batch_results = await getStockDataV3BatchChunked(
            session,
            unique_symbols,
            period="2y",
            interval="1d",
            batch_size=50
        )

        download_time = time.time() - download_start
        print(f"\n✅ Batch download completed in {download_time:.2f} seconds")
        print(f"📈 Successfully downloaded {len(batch_results)}/{len(unique_symbols)} symbols")
        print(f"⚡ Average: {download_time/len(batch_results):.3f} seconds per symbol")
        print(f"\n🔍 Processing symbols for inside days and pocket pivot patterns...")        # Process the batch results
        for symbol, stock_df in batch_results.items():
            try:
                if stock_df is None or stock_df.empty:
                    print(f"Warning: No data available for {symbol}")
                    failed_symbols.append(symbol)
                    continue

                # Check if required columns exist
                required_columns = ['inside_day', 'volume_pocket_pivot']
                if 'IBD_Ants_Signal' in stock_df.columns:
                    required_columns.append('IBD_Ants_Signal')

                missing_columns = [col for col in required_columns if col not in stock_df.columns]
                if missing_columns and not ('IBD_Ants_Signal' in missing_columns and len(missing_columns) == 1):
                    print(f"Warning: Missing pattern columns for {symbol}: {missing_columns}")
                    failed_symbols.append(symbol)
                    continue

                # Check most recent day (index 0) for all conditions
                has_inside_day = bool(stock_df['inside_day'].iloc[0])
                has_pocket_pivot = bool(stock_df['volume_pocket_pivot'].iloc[0])
                has_ants = 'IBD_Ants_Signal' in stock_df.columns and bool(stock_df['IBD_Ants_Signal'].iloc[0])

                # Categorize based on pattern combinations
                if has_inside_day and has_pocket_pivot and has_ants:
                    print(f"🎯 ALL THREE patterns found for: {symbol}")
                    all_three_symbols.append(symbol)
                elif has_inside_day and has_pocket_pivot:
                    print(f"✅ Inside day AND pocket pivot found for: {symbol}")
                    both_symbols.append(symbol)
                elif has_ants and has_inside_day:
                    print(f"🐜 Ants AND inside day found for: {symbol}")
                    ants_and_inside_day_symbols.append(symbol)
                elif has_ants and has_pocket_pivot:
                    print(f"🐜 Ants AND pocket pivot found for: {symbol}")
                    ants_and_pocket_pivot_symbols.append(symbol)
                elif has_inside_day:
                    print(f"🔵 Inside day only found for: {symbol}")
                    inside_day_only_symbols.append(symbol)
                elif has_pocket_pivot:
                    print(f"🟠 Pocket pivot only found for: {symbol}")
                    pocket_pivot_only_symbols.append(symbol)
                elif has_ants:
                    print(f"🐜 IBD Ants signal only found for: {symbol}")
                    ants_only_symbols.append(symbol)

            except Exception as e:
                print(f"Error processing {symbol}: {e}")
                failed_symbols.append(symbol)

        # Report on any symbols that failed to download
        if failed_symbols:
            print(f"\n⚠️ Failed to process {len(failed_symbols)} symbols:")
            print("   " + ", ".join(failed_symbols[:10]))  # Show first 10
            if len(failed_symbols) > 10:
                print(f"   ... and {len(failed_symbols) - 10} more")

    # Calculate total processing time
    total_time = time.time() - start_time

    # Print summary statistics
    print(f"\n📊 SCANNING SUMMARY")
    print("=" * 60)
    print(f"⏱️ Total processing time: {total_time:.2f} seconds")
    print(f"📈 Symbols processed: {len(batch_results)}/{len(unique_symbols)}")
    print(f"🎯 Inside day only: {len(inside_day_only_symbols)}")
    print(f"🎯 Pocket pivot only: {len(pocket_pivot_only_symbols)}")
    print(f"🐜 IBD Ants only: {len(ants_only_symbols)}")
    print(f"✅ Inside day + Pocket pivot: {len(both_symbols)}")
    print(f"🐜 Ants + Inside day: {len(ants_and_inside_day_symbols)}")
    print(f"🐜 Ants + Pocket pivot: {len(ants_and_pocket_pivot_symbols)}")
    print(f"🎯 All three patterns: {len(all_three_symbols)}")
    print(f"❌ Failed: {len(failed_symbols)}")

    # Print the three separate lists
    print(f"\n🔵 INSIDE DAY ONLY ({len(inside_day_only_symbols)} symbols):")
    print("=" * 50)
    if inside_day_only_symbols:
        print(','.join(inside_day_only_symbols))
    else:
        print("No symbols with inside days only found.")

    print(f"\n🟠 POCKET PIVOT ONLY ({len(pocket_pivot_only_symbols)} symbols):")
    print("=" * 50)
    if pocket_pivot_only_symbols:
        print(','.join(pocket_pivot_only_symbols))
    else:
        print("No symbols with pocket pivots only found.")

    print(f"\n🐜 IBD ANTS ONLY ({len(ants_only_symbols)} symbols):")
    print("=" * 50)
    if ants_only_symbols:
        print(','.join(ants_only_symbols))
    else:
        print("No symbols with IBD Ants signals only found.")

    print(f"\n✅ INSIDE DAY + POCKET PIVOT ({len(both_symbols)} symbols):")
    print("=" * 50)
    if both_symbols:
        print(','.join(both_symbols))
    else:
        print("No symbols with both inside day and pocket pivot found.")

    print(f"\n🐜 IBD ANTS + INSIDE DAY ({len(ants_and_inside_day_symbols)} symbols):")
    print("=" * 50)
    if ants_and_inside_day_symbols:
        print(','.join(ants_and_inside_day_symbols))
    else:
        print("No symbols with IBD Ants and inside day found.")

    print(f"\n🐜 IBD ANTS + POCKET PIVOT ({len(ants_and_pocket_pivot_symbols)} symbols):")
    print("=" * 50)
    if ants_and_pocket_pivot_symbols:
        print(','.join(ants_and_pocket_pivot_symbols))
    else:
        print("No symbols with IBD Ants and pocket pivot found.")

    print(f"\n🎯 ALL THREE PATTERNS ({len(all_three_symbols)} symbols):")
    print("=" * 50)
    if all_three_symbols:
        print(','.join(all_three_symbols))
    else:
        print("No symbols with all three patterns found.")

    print("\n" + "=" * 60)
    print(f"🏁 Daily Scanner Completed - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Performance improvement estimate
    estimated_sequential_time = len(unique_symbols) * 2.0  # Assume 2 seconds per symbol sequentially
    if total_time < estimated_sequential_time:
        speedup = estimated_sequential_time / total_time
        print(f"⚡ Estimated speedup: {speedup:.1f}x faster than sequential processing")

if __name__ == "__main__":
    asyncio.run(main())
