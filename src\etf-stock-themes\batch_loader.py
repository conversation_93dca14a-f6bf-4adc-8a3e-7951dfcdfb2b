#!/usr/bin/env python3
"""
Batch Stock Loader with Progress Tracking and Error Handling
"""

import sys
import os
import time
import json
import ast
import asyncio
from datetime import datetime
from typing import List, Dict, Set
import pandas as pd

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

from enhanced_theme_etf_collector import EnhancedThemeETFCollector

class BatchLoader:
    def __init__(self):
        self.collector = EnhancedThemeETFCollector()
        self.start_time = None
        self.stats = {
            'total_stocks': 0,
            'processed_stocks': 0,
            'skipped_stocks': 0,
            'failed_stocks': 0,
            'new_etfs_found': 0,
            'total_etfs_processed': 0,
            'errors': []
        }
        self.failed_stocks = []
        self.skipped_stocks = []
        self.processed_stocks = []

        # Create logs directory
        self.logs_dir = os.path.join(current_dir, "batch_logs")
        os.makedirs(self.logs_dir, exist_ok=True)

        # Setup log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(self.logs_dir, f"batch_load_{timestamp}.log")

    def log(self, message: str, level: str = "INFO"):
        """Log message to both console and file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"

        print(log_entry)

        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")

    def load_stocks_from_file(self, file_path: str) -> List[str]:
        """Load stocks from batch_load.txt file"""
        try:
            with open(file_path, 'r') as f:
                content = f.read().strip()

            # Parse the Python list format
            stocks = ast.literal_eval(content)

            if not isinstance(stocks, list):
                raise ValueError("File content is not a list")

            self.log(f"Loaded {len(stocks)} stocks from {file_path}")
            return stocks

        except Exception as e:
            self.log(f"Error loading stocks from file: {e}", "ERROR")
            return []

    def print_progress(self, current: int, total: int, stock: str, status: str):
        """Print progress bar and current status"""
        percentage = (current / total) * 100
        bar_length = 40
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)

        elapsed = time.time() - self.start_time
        if current > 0:
            eta = (elapsed / current) * (total - current)
            eta_str = f"ETA: {int(eta//60):02d}:{int(eta%60):02d}"
        else:
            eta_str = "ETA: --:--"

        print(f"\r[{bar}] {percentage:5.1f}% ({current}/{total}) | {stock:<8} | {status:<20} | {eta_str}", end='', flush=True)

    async def process_single_stock(self, stock: str) -> Dict:
        """Process a single stock and return results"""
        result = {
            'stock': stock,
            'status': 'unknown',
            'etfs_found': 0,
            'new_etfs': 0,
            'error': None,
            'processing_time': 0
        }

        start_time = time.time()

        try:
            # Check if stock already processed
            if self.collector.has_stock_been_processed(stock):
                existing_count = self.collector.count_theme_etfs_for_stock(stock)
                result.update({
                    'status': 'already_processed',
                    'etfs_found': existing_count,
                    'processing_time': time.time() - start_time
                })
                return result

            # Process the stock (async call)
            process_result = await self.collector.process_stock(stock)

            if process_result and process_result.get('theme_etfs_found', 0) > 0:
                result.update({
                    'status': 'success',
                    'etfs_found': process_result.get('theme_etfs_found', 0),
                    'new_etfs': process_result.get('new_etfs_added', 0),
                    'processing_time': time.time() - start_time
                })
            else:
                result.update({
                    'status': 'no_etfs_found',
                    'processing_time': time.time() - start_time
                })

        except Exception as e:
            result.update({
                'status': 'error',
                'error': str(e),
                'processing_time': time.time() - start_time
            })

        return result

    def save_progress_checkpoint(self, current_index: int, stocks: List[str]):
        """Save current progress to checkpoint file"""
        checkpoint = {
            'timestamp': datetime.now().isoformat(),
            'current_index': current_index,
            'total_stocks': len(stocks),
            'stats': self.stats,
            'failed_stocks': self.failed_stocks,
            'skipped_stocks': self.skipped_stocks,
            'processed_stocks': self.processed_stocks
        }

        checkpoint_file = os.path.join(self.logs_dir, "batch_progress_checkpoint.json")
        with open(checkpoint_file, 'w') as f:
            json.dump(checkpoint, f, indent=2)

    def load_checkpoint(self) -> Dict:
        """Load previous checkpoint if exists"""
        checkpoint_file = os.path.join(self.logs_dir, "batch_progress_checkpoint.json")
        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.log(f"Error loading checkpoint: {e}", "WARNING")
        return None

    async def run_batch_load(self, file_path: str, resume: bool = False):
        """Run the batch loading process"""

        self.log("🚀 Starting Batch Stock Loading Process")
        self.log("=" * 60)

        # Load stocks
        stocks = self.load_stocks_from_file(file_path)
        if not stocks:
            self.log("No stocks to process. Exiting.", "ERROR")
            return

        self.stats['total_stocks'] = len(stocks)
        start_index = 0

        # Check for resume
        if resume:
            checkpoint = self.load_checkpoint()
            if checkpoint:
                start_index = checkpoint.get('current_index', 0)
                self.stats = checkpoint.get('stats', self.stats)
                self.failed_stocks = checkpoint.get('failed_stocks', [])
                self.skipped_stocks = checkpoint.get('skipped_stocks', [])
                self.processed_stocks = checkpoint.get('processed_stocks', [])
                self.log(f"📂 Resuming from checkpoint: stock {start_index + 1}/{len(stocks)}")

        self.start_time = time.time()

        # Process stocks
        for i, stock in enumerate(stocks[start_index:], start_index):

            # Print progress
            self.print_progress(i, len(stocks), stock, "Processing...")

            # Process stock
            result = await self.process_single_stock(stock)

            # Update stats
            if result['status'] == 'success':
                self.stats['processed_stocks'] += 1
                self.stats['new_etfs_found'] += result['new_etfs']
                self.stats['total_etfs_processed'] += result['etfs_found']
                self.processed_stocks.append(stock)
                status_msg = f"✅ {result['etfs_found']} ETFs"

            elif result['status'] == 'already_processed':
                self.stats['skipped_stocks'] += 1
                self.stats['total_etfs_processed'] += result['etfs_found']
                self.skipped_stocks.append(stock)
                status_msg = f"⏭️  Already done ({result['etfs_found']} ETFs)"

            elif result['status'] == 'no_etfs_found':
                self.stats['processed_stocks'] += 1
                self.processed_stocks.append(stock)
                status_msg = "🔍 No theme ETFs"

            else:  # error
                self.stats['failed_stocks'] += 1
                self.stats['errors'].append(f"{stock}: {result['error']}")
                self.failed_stocks.append(stock)
                status_msg = f"❌ Error"
                self.log(f"Error processing {stock}: {result['error']}", "ERROR")

            # Update progress display
            self.print_progress(i + 1, len(stocks), stock, status_msg)

            # Save checkpoint every 10 stocks
            if (i + 1) % 10 == 0:
                self.save_progress_checkpoint(i + 1, stocks)

            # Small delay to avoid overwhelming APIs
            time.sleep(0.1)

        print()  # New line after progress bar
        self.finalize_batch_load()

    def finalize_batch_load(self):
        """Finalize the batch loading process"""

        total_time = time.time() - self.start_time

        self.log("\n" + "=" * 60)
        self.log("🎉 Batch Loading Complete!")
        self.log("=" * 60)

        # Print final statistics
        self.log(f"📊 Final Statistics:")
        self.log(f"   Total stocks: {self.stats['total_stocks']}")
        self.log(f"   Successfully processed: {self.stats['processed_stocks']}")
        self.log(f"   Already processed (skipped): {self.stats['skipped_stocks']}")
        self.log(f"   Failed: {self.stats['failed_stocks']}")
        self.log(f"   New ETFs discovered: {self.stats['new_etfs_found']}")
        self.log(f"   Total ETFs processed: {self.stats['total_etfs_processed']}")
        self.log(f"   Total processing time: {int(total_time//60):02d}:{int(total_time%60):02d}")

        if self.stats['processed_stocks'] > 0:
            avg_time = total_time / (self.stats['processed_stocks'] + self.stats['skipped_stocks'])
            self.log(f"   Average time per stock: {avg_time:.2f}s")

        # Rebuild cache
        self.log("\n🔄 Rebuilding stock-niches cache...")
        self.collector.rebuild_stock_niches_cache()
        self.collector.print_cache_stats()

        # Save final report
        self.save_final_report()

        if self.failed_stocks:
            self.log(f"\n⚠️  Failed stocks ({len(self.failed_stocks)}): {', '.join(self.failed_stocks[:10])}{'...' if len(self.failed_stocks) > 10 else ''}")

        self.log(f"\n📁 Logs saved to: {self.log_file}")

    def save_final_report(self):
        """Save final batch loading report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'stats': self.stats,
            'processed_stocks': self.processed_stocks,
            'skipped_stocks': self.skipped_stocks,
            'failed_stocks': self.failed_stocks,
            'total_time_seconds': time.time() - self.start_time
        }

        report_file = os.path.join(self.logs_dir, f"batch_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        self.log(f"📋 Final report saved to: {report_file}")

async def main():
    import argparse

    parser = argparse.ArgumentParser(description='Batch load stocks with progress tracking')
    parser.add_argument('--file', default='src/etf-stock-themes/batch_load.txt', help='File containing stocks to load')
    parser.add_argument('--resume', action='store_true', help='Resume from previous checkpoint')

    args = parser.parse_args()

    loader = BatchLoader()
    await loader.run_batch_load(args.file, resume=args.resume)

if __name__ == "__main__":
    asyncio.run(main())
