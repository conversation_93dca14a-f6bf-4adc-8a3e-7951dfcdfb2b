import sys
import os
import asyncio
import aiohttp

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))

from util.scrapper import etfHoldingsETFDBdotCOM, fundHoldingsYahoo
from tabulate import tabulate

async def test_etf_holdings(ticker):
    """Test the etfHoldingsETFDBdotCOM function with ticker."""
    print(f"Testing etfHoldingsETFDBdotCOM with ticker: {ticker}")

    # Create async session
    async with aiohttp.ClientSession() as session:
        try:
            result = await etfHoldingsETFDBdotCOM(session, ticker)

            # Print the column names to verify they're fixed
            if result is not None and hasattr(result, 'columns'):
                print(f"Column names: {result.columns.tolist()}")

                # Print the result
                if not result.empty:
                    print(f"Success! Found {len(result)} rows of ETF holdings data.")
                    print("\nETF Holdings:")
                    print(tabulate(result, headers='keys', tablefmt='fancy_grid', showindex=False))

                    # Print ETF values as comma-separated
                    if 'ETF' in result.columns:
                        # Get the first ETF column (there are duplicate column names)
                        etf_values = result.iloc[:, 1].tolist()  # Second column (index 1) is ETF names
                        print(f"\nETF values (comma-separated): {','.join(etf_values)}")

                else:
                    print("Empty DataFrame returned.")
            else:
                print("No data returned or invalid DataFrame.")
        except Exception as e:
            print(f"Error testing ETF holdings: {e}")

async def test_fund_holdings(ticker):
    """Test the fundHoldingsYahoo function with ticker."""
    print(f"\nTesting fundHoldingsYahoo with ticker: {ticker}")

    # Create async session
    async with aiohttp.ClientSession() as session:
        try:
            result = await fundHoldingsYahoo(session, ticker)

            # Print the column names to verify they're fixed
            if result is not None and hasattr(result, 'columns'):
                print(f"Column names: {result.columns.tolist()}")

                # Print the result
                if not result.empty:
                    print(f"Success! Found {len(result)} rows of fund holdings data.")
                    print("\nFund Holdings:")
                    print(tabulate(result, headers='keys', tablefmt='fancy_grid', showindex=False))

                    # Print ticker values as comma-separated
                    if 'Ticker' in result.columns:
                        ticker_values = result['Ticker'].tolist()
                        print(f"\nTicker values (comma-separated): {','.join(ticker_values)}")

                else:
                    print("Empty DataFrame returned.")
            else:
                print("No data returned or invalid DataFrame.")
        except Exception as e:
            print(f"Error testing fund holdings: {e}")

async def main():
    await test_etf_holdings(ticker='HOOD')
    #await test_fund_holdings('AMD')

if __name__ == "__main__":
    asyncio.run(main())