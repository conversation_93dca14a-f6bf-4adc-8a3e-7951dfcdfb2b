#!/usr/bin/env python3
"""
Quick start script for batch loading
"""

import sys
import os
import asyncio

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

from batch_loader import BatchLoader

async def main():
    print("🚀 ETF Stock Theme Batch Loader")
    print("=" * 50)

    # Check if batch_load.txt exists
    batch_file = os.path.join(current_dir, "batch_load.txt")
    if not os.path.exists(batch_file):
        print(f"❌ Error: {batch_file} not found!")
        return

    # Show preview of stocks
    try:
        with open(batch_file, 'r') as f:
            content = f.read().strip()

        import ast
        stocks = ast.literal_eval(content)

        print(f"📋 Found {len(stocks)} stocks to process")
        print(f"📝 First 10 stocks: {', '.join(stocks[:10])}")
        if len(stocks) > 10:
            print(f"    ... and {len(stocks) - 10} more")

    except Exception as e:
        print(f"❌ Error reading batch file: {e}")
        return

    # Ask for confirmation
    print(f"\n⚠️  This will process {len(stocks)} stocks.")
    print("   Each stock may take 5-30 seconds depending on ETF holdings.")
    print(f"   Estimated total time: {len(stocks) * 10 // 60} - {len(stocks) * 30 // 60} minutes")

    response = input("\n🤔 Do you want to proceed? (y/N): ").strip().lower()

    if response not in ['y', 'yes']:
        print("👋 Batch loading cancelled.")
        return

    # Check for existing checkpoint
    loader = BatchLoader()
    checkpoint = loader.load_checkpoint()

    if checkpoint:
        current_index = checkpoint.get('current_index', 0)
        total_stocks = checkpoint.get('total_stocks', len(stocks))

        print(f"\n📂 Found previous checkpoint:")
        print(f"   Progress: {current_index}/{total_stocks} stocks")
        print(f"   Processed: {checkpoint.get('stats', {}).get('processed_stocks', 0)}")
        print(f"   Failed: {checkpoint.get('stats', {}).get('failed_stocks', 0)}")

        resume_response = input("\n🔄 Resume from checkpoint? (Y/n): ").strip().lower()
        resume = resume_response not in ['n', 'no']
    else:
        resume = False

    # Start batch loading
    print(f"\n🚀 Starting batch load...")
    print("   Progress will be saved every 10 stocks")
    print("   Press Ctrl+C to stop (progress will be saved)")
    print("   Logs will be saved to: src/etf-stock-themes/batch_logs/")

    try:
        await loader.run_batch_load(batch_file, resume=resume)
    except KeyboardInterrupt:
        print(f"\n\n⏹️  Batch loading interrupted by user")
        print("   Progress has been saved. Use --resume to continue later.")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        print("   Check the log files for details.")

if __name__ == "__main__":
    asyncio.run(main())
