#!/usr/bin/env python3
"""
ETF Niche Analytics Module
Provides analytics for ETF theme data with RS rating integration
"""

import os
import sys
import json
import pandas as pd
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(src_dir)

class ETFNicheAnalytics:
    def __init__(self):
        self.etf_data_dir = os.path.join(src_dir, "data", "etf-data")
        self.rs_data_dir = os.path.join(src_dir, "data", "rs_data")

        # Cache for loaded data
        self._stock_niches_cache = None
        self._etf_data_cache = None
        self._rs_data_cache = None

    def load_stock_niches_cache(self) -> Dict[str, List[str]]:
        """Load the stock-niches cache from JSON file"""
        if self._stock_niches_cache is not None:
            return self._stock_niches_cache

        cache_file = os.path.join(self.etf_data_dir, "stock_niches_cache.json")

        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    self._stock_niches_cache = json.load(f)
                return self._stock_niches_cache
            except Exception as e:
                print(f"Error loading stock-niches cache: {e}")

        return {}

    def load_etf_data(self) -> pd.DataFrame:
        """Load the full ETF data from CSV"""
        if self._etf_data_cache is not None:
            return self._etf_data_cache

        csv_file = os.path.join(self.etf_data_dir, "theme_etfs.csv")

        if os.path.exists(csv_file):
            try:
                self._etf_data_cache = pd.read_csv(csv_file)
                return self._etf_data_cache
            except Exception as e:
                print(f"Error loading ETF data: {e}")

        return pd.DataFrame()

    def load_rs_data(self) -> pd.DataFrame:
        """Load RS rating data"""
        if self._rs_data_cache is not None:
            return self._rs_data_cache

        rs_file = os.path.join(self.rs_data_dir, "rs_stocks_winsorized.csv")

        if os.path.exists(rs_file):
            try:
                self._rs_data_cache = pd.read_csv(rs_file)
                return self._rs_data_cache
            except Exception as e:
                print(f"Error loading RS data: {e}")

        return pd.DataFrame()

    def get_niche_distribution(self, screener_stocks: List[str] = None) -> Dict[str, Dict]:
        """Get distribution of stocks across niches with RS rating analysis

        Args:
            screener_stocks: List of stock tickers from screener. If None, uses all ETF stocks.
        """

        stock_niches = self.load_stock_niches_cache()
        rs_data = self.load_rs_data()

        # Filter to only screener stocks if provided
        if screener_stocks:
            screener_set = set(ticker.upper() for ticker in screener_stocks)
            stock_niches = {stock: niches for stock, niches in stock_niches.items()
                          if stock.upper() in screener_set}

        # Create RS rating lookup
        rs_lookup = {}
        if not rs_data.empty and 'Ticker' in rs_data.columns and 'Percentile' in rs_data.columns:
            rs_lookup = dict(zip(rs_data['Ticker'], rs_data['Percentile']))

        # Analyze niche distribution
        niche_stats = defaultdict(lambda: {
            'stock_count': 0,
            'stocks': [],
            'rs_ratings': [],
            'avg_rs_rating': 0,
            'high_rs_count': 0,  # RS >= 70
            'exceptional_rs_count': 0  # RS >= 90
        })

        for stock, niches in stock_niches.items():
            rs_rating = rs_lookup.get(stock, 0)

            for niche in niches:
                niche_stats[niche]['stock_count'] += 1
                niche_stats[niche]['stocks'].append(stock)
                niche_stats[niche]['rs_ratings'].append(rs_rating)

                if rs_rating >= 70:
                    niche_stats[niche]['high_rs_count'] += 1
                if rs_rating >= 90:
                    niche_stats[niche]['exceptional_rs_count'] += 1

        # Calculate averages and scores
        for niche, stats in niche_stats.items():
            if stats['rs_ratings']:
                stats['avg_rs_rating'] = sum(stats['rs_ratings']) / len(stats['rs_ratings'])

            # Calculate niche strength score (combination of stock count and RS quality)
            stock_count_score = min(stats['stock_count'] / 10, 1.0) * 50  # Max 50 points for stock count
            rs_quality_score = (stats['avg_rs_rating'] / 100) * 50  # Max 50 points for RS quality
            stats['strength_score'] = stock_count_score + rs_quality_score

        return dict(niche_stats)

    def get_pie_chart_data(self, screener_stocks: List[str] = None) -> Tuple[List[str], List[int], List[float]]:
        """Get data formatted for pie chart: labels, values, scores"""

        niche_distribution = self.get_niche_distribution(screener_stocks)

        # Sort by strength score descending
        sorted_niches = sorted(
            niche_distribution.items(),
            key=lambda x: x[1]['strength_score'],
            reverse=True
        )

        labels = []
        values = []
        scores = []

        for niche, stats in sorted_niches:
            labels.append(niche)
            values.append(stats['stock_count'])
            scores.append(stats['strength_score'])

        return labels, values, scores

    def get_niche_details(self, niche: str) -> Dict:
        """Get detailed information about a specific niche"""

        niche_distribution = self.get_niche_distribution()

        if niche not in niche_distribution:
            return {}

        stats = niche_distribution[niche]
        rs_data = self.load_rs_data()
        rs_lookup = {}

        if not rs_data.empty and 'Ticker' in rs_data.columns and 'Percentile' in rs_data.columns:
            rs_lookup = dict(zip(rs_data['Ticker'], rs_data['Percentile']))

        # Get detailed stock information
        stock_details = []
        for stock in stats['stocks']:
            rs_rating = rs_lookup.get(stock, 0)
            stock_details.append({
                'ticker': stock,
                'rs_rating': rs_rating,
                'rs_category': 'Exceptional' if rs_rating >= 90 else 'Strong' if rs_rating >= 70 else 'Moderate' if rs_rating >= 50 else 'Weak'
            })

        # Sort by RS rating descending
        stock_details.sort(key=lambda x: x['rs_rating'], reverse=True)

        return {
            'niche': niche,
            'total_stocks': stats['stock_count'],
            'avg_rs_rating': stats['avg_rs_rating'],
            'strength_score': stats['strength_score'],
            'high_rs_count': stats['high_rs_count'],
            'exceptional_rs_count': stats['exceptional_rs_count'],
            'stock_details': stock_details
        }

    def get_top_niches(self, limit: int = 10, screener_stocks: List[str] = None) -> List[Dict]:
        """Get top niches by strength score"""

        niche_distribution = self.get_niche_distribution(screener_stocks)

        # Sort by strength score descending
        sorted_niches = sorted(
            niche_distribution.items(),
            key=lambda x: x[1]['strength_score'],
            reverse=True
        )

        top_niches = []
        for niche, stats in sorted_niches[:limit]:
            top_niches.append({
                'niche': niche,
                'stock_count': stats['stock_count'],
                'avg_rs_rating': stats['avg_rs_rating'],
                'strength_score': stats['strength_score'],
                'high_rs_count': stats['high_rs_count'],
                'exceptional_rs_count': stats['exceptional_rs_count']
            })

        return top_niches

    def get_analytics_summary(self, screener_stocks: List[str] = None) -> Dict:
        """Get overall analytics summary"""

        stock_niches = self.load_stock_niches_cache()

        # Filter to screener stocks if provided
        if screener_stocks:
            screener_set = set(ticker.upper() for ticker in screener_stocks)
            stock_niches = {stock: niches for stock, niches in stock_niches.items()
                          if stock.upper() in screener_set}

        niche_distribution = self.get_niche_distribution(screener_stocks)

        total_stocks = len(stock_niches)
        total_niches = len(niche_distribution)

        # Calculate total combinations
        total_combinations = sum(len(niches) for niches in stock_niches.values())

        # Get top niche by strength
        if niche_distribution:
            top_niche = max(niche_distribution.items(), key=lambda x: x[1]['strength_score'])
            top_niche_name = top_niche[0]
            top_niche_score = top_niche[1]['strength_score']
        else:
            top_niche_name = "None"
            top_niche_score = 0

        return {
            'total_stocks': total_stocks,
            'total_niches': total_niches,
            'total_combinations': total_combinations,
            'avg_niches_per_stock': total_combinations / total_stocks if total_stocks > 0 else 0,
            'top_niche': top_niche_name,
            'top_niche_score': top_niche_score
        }

    def get_stocks_by_theme(self, theme: str, screener_stocks: List[str] = None) -> List[str]:
        """Get list of stocks belonging to a specific theme"""

        stock_niches = self.load_stock_niches_cache()

        # Filter to screener stocks if provided
        if screener_stocks:
            screener_set = set(ticker.upper() for ticker in screener_stocks)
            stock_niches = {stock: niches for stock, niches in stock_niches.items()
                          if stock.upper() in screener_set}

        theme_stocks = []
        for stock, niches in stock_niches.items():
            if theme in niches:
                theme_stocks.append(stock)

        return theme_stocks

    def get_available_themes(self, screener_stocks: List[str] = None) -> List[str]:
        """Get list of all available themes for the given stocks"""

        niche_distribution = self.get_niche_distribution(screener_stocks)
        return sorted(niche_distribution.keys())

# Global instance for easy access
etf_analytics = ETFNicheAnalytics()
