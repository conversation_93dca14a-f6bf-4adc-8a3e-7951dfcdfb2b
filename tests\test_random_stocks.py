import pandas as pd
import asyncio
import aiohttp
import sys
import os
import random

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from util.scrapper import etfHoldingsETFDBdotCOM
import requests
import json

# Ollama configuration
OLLAMA_URL = "http://localhost:11434/api/generate"
MODEL = "gemma3:1b"

def build_prompt(company_description: str, etf_names: list[str]) -> str:
    template = """You are a specialized AI that identifies thematic investment keywords for stocks.

TASK: Analyze the provided stock description and the names of ETFs that hold it. Based on this information, generate 3 to 8 unique, lowercase, comma-separated thematic keywords that describe the company's specific business focus.

RULES:
- Focus on specific, niche themes (e.g., gene editing, cybersecurity, cloud computing).
- Output must be a single line of lowercase, comma-separated keywords.
- DO NOT use generic words like technology, growth, global, value, innovation, industrial, etc.
- DO NOT use words from ETF provider names or types, like ishares, spdr, vanguard, etf, fund, trust, etc.

EXAMPLE 1:
INPUT:
Stock: NVIDIA (NVDA)
Description: Designs graphics processing units (GPUs) for gaming, professional visualization, data centers, and automotive markets. They are a leader in artificial intelligence hardware and software.
ETFs: VanEck Semiconductor ETF, Global X Robotics & Artificial Intelligence ETF, iShares PHLX Semiconductor ETF, PDR S&P Kensho Smart Mobility ETF.
OUTPUT:
semiconductors, artificial intelligence, data centers, gaming hardware, autonomous vehicles, gpu manufacturing

EXAMPLE 2:
INPUT:
Stock: UBER
Description: Operates a technology platform in three segments: Mobility (ridesharing, micromobility), Delivery (food, grocery), and Freight (logistics network).
ETFs: iShares US Transportation ETF, Amplify Travel Tech ETF, ProShares On-Demand ETF, Fidelity Electric Vehicles and Future Transportation ETF, Global X Millennial Consumer ETF.
OUTPUT:
ridesharing, gig economy, food delivery, logistics tech, on-demand services, mobility-as-a-service

NOW, COMPLETE THIS TASK:
INPUT:
Description: {desc}
ETFs: {etfs}
OUTPUT:"""
    return template.format(
        desc=(company_description or "").strip(),
        etfs=", ".join([e.strip() for e in etf_names])
    )

def generate_themes(company_description: str, etf_names: list[str]) -> str:
    prompt = build_prompt(company_description, etf_names)
    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.3,
            "top_p": 0.9
        }
    }
    try:
        resp = requests.post(OLLAMA_URL, json=payload, timeout=120)
        resp.raise_for_status()
        data = resp.json()
        text = data.get("response", "").strip()
        text = text.strip("` \n").lower()
        themes = [t.strip() for t in text.split(",") if t.strip()]
        seen, deduped = set(), []
        for t in themes:
            if t not in seen:
                seen.add(t)
                deduped.append(t)
        return ", ".join(deduped)
    except Exception as e:
        print(f"Error generating themes with Ollama: {e}")
        return "error_generating_themes"

async def get_etf_holdings(session, ticker):
    """Get ETF holdings for a ticker."""
    try:
        result = await etfHoldingsETFDBdotCOM(session, ticker)
        if result is not None and hasattr(result, 'columns') and not result.empty:
            if len(result.columns) > 1:
                etf_values = result.iloc[:, 1].tolist()
                return etf_values
        return []
    except Exception as e:
        print(f"Error getting ETF holdings for {ticker}: {e}")
        return []

async def test_random_stocks():
    """Test theme generation with 5 random stocks from screener file."""

    # Load screener data
    screener_file = 'src/data/screener/filtered_sorted_screener_2025-09-24.xlsx'
    try:
        df = pd.read_excel(screener_file)
        print(f"Loaded {len(df)} rows from screener file")

        # Get 5 random stocks
        random_stocks = df.sample(n=5, random_state=42)  # Fixed seed for reproducible results

    except Exception as e:
        print(f"Error loading screener file: {e}")
        return

    print("="*80)
    print("TESTING THEME GENERATION WITH 5 RANDOM STOCKS")
    print("="*80)

    async with aiohttp.ClientSession() as session:
        for idx, (_, row) in enumerate(random_stocks.iterrows(), 1):
            ticker = row['Symbol']
            description = row['Description']

            print(f"\n[{idx}/5] TESTING: {ticker}")
            print("-" * 60)
            print(f"Description: {description[:200]}{'...' if len(description) > 200 else ''}")

            # Get ETF holdings
            etf_names = await get_etf_holdings(session, ticker)

            if etf_names:
                print(f"\nETF Holdings ({len(etf_names)} found):")
                for i, etf in enumerate(etf_names[:5]):  # Show first 5
                    print(f"  {i+1}. {etf}")
                if len(etf_names) > 5:
                    print(f"  ... and {len(etf_names)-5} more")

                # Generate themes
                print(f"\nGenerating themes...")
                themes = generate_themes(description, etf_names)
                print(f"✅ GENERATED THEMES: {themes}")

            else:
                print(f"❌ No ETF holdings found for {ticker}")

            print("-" * 60)

    print("\n" + "="*80)
    print("TEST COMPLETED")
    print("="*80)

if __name__ == "__main__":
    asyncio.run(test_random_stocks())