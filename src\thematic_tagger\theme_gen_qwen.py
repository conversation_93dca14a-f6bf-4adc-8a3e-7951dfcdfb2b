import sys
import os
import asyncio
import aiohttp
import pandas as pd
import requests
import json
import re

# Add the src directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from util.scrapper import etfHoldingsETFDBdotCOM

# Ollama configuration
OLLAMA_URL = "http://localhost:11434/api/generate"
MODEL = 'qwen2.5:3b'

def build_prompt(ticker: str, company_description: str, etf_names: list[str], sector: str, industry: str, sub_industry: str) -> str:
    """Build optimized prompt for Qwen2.5 using proper chat template."""
    template = """<|im_start|>system
You are an expert thematic investment analyst. Your task is to generate precise investment themes that describe what companies actually do - their core products, services, and markets.

STRICT RULES:
- NO financial terms: growth, value, smallcap, bonds, rates, dividends, ipo
- NO generic business jargon: business, technology, economics, industrial, company, consumer
- ONLY specific themes about products/services/markets/technologies
- Output exactly 3 themes in lowercase, comma-separated
- Focus on what the company ACTUALLY MAKES or DOES<|im_end|>

<|im_start|>user
Analyze this company using the 3-step process:

**Company:** {ticker}
**Sector:** {sector}
**Industry:** {industry}
**Sub-Industry:** {sub_industry}
**Description:** {desc}
**ETFs holding this stock:** {etfs}

**Step 1: Candidate Generation**
List all potential themes from the description and ETF names.

**Step 2: Aggressive Filtering**
Remove any themes that are:
- Financial/market terms (growth, value, smallcap, etc.)
- Generic business jargon (business, technology, economics, etc.)
- Too vague or not core to the business

**Step 3: Final Selection**
Choose exactly 3 most specific and relevant themes.

**Examples:**
✅ Good themes: semiconductors, ai, gaming, healthcare, energy, aerospace, pharmaceuticals, robotics, data, oil, mining, biotech
❌ Bad themes: technology, business, growth, consumer, industrial, economics, smallcap, value

**Required Output Format:**
End your response with exactly this format:
THEMES: theme1, theme2, theme3<|im_end|>

<|im_start|>assistant
Let me analyze {ticker} step by step:

**Step 1: Candidate Generation**
From the description and ETF context, potential themes include: """

    return template.format(
        ticker=ticker,
        desc=(company_description or "No description available").strip(),
        etfs=", ".join([e.strip() for e in etf_names]) if etf_names else "None found",
        sector=sector or "N/A",
        industry=industry or "N/A",
        sub_industry=sub_industry or "N/A"
    )

def build_prompt_simple(ticker: str, company_description: str, etf_names: list[str], sector: str, industry: str, sub_industry: str) -> str:
    """Simplified version for faster processing."""
    template = """<|im_start|>system
You are a thematic investment analyst. Generate exactly 3 investment themes for companies based on what they actually do.

Rules:
- No financial terms (growth, value, smallcap)
- No generic terms (business, technology, economics)
- Only specific products/services/markets
- Output format: theme1, theme2, theme3 (lowercase, comma-separated)<|im_end|>

<|im_start|>user
Company: {ticker}
Business: {desc}
Sector: {sector}
ETFs: {etfs}

Generate 3 specific investment themes:<|im_end|>

<|im_start|>assistant
Based on the analysis, the investment themes are:

"""

    return template.format(
        ticker=ticker,
        desc=(company_description or "No description available").strip(),
        etfs=", ".join([e.strip() for e in etf_names[:3]]) if etf_names else "None",
        sector=sector or "N/A"
    )

def generate_themes(ticker: str, company_description: str, etf_names: list[str], sector: str, industry: str, sub_industry: str, use_simple: bool = False) -> str:
    """Generate themes using Qwen2.5 with optimized parameters."""

    if use_simple:
        prompt = build_prompt_simple(ticker, company_description, etf_names, sector, industry, sub_industry)
    else:
        prompt = build_prompt(ticker, company_description, etf_names, sector, industry, sub_industry)

    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.1,
            "top_p": 0.85,
            "top_k": 20,
            "repeat_penalty": 1.1,
            "num_ctx": 2048,
            "stop": ["\n\n", "<|im_end|>", "**Step", "**", "EXAMPLES:"]
        }
    }

    try:
        resp = requests.post(OLLAMA_URL, json=payload, timeout=300)
        resp.raise_for_status()
        data = resp.json()
        text = data.get("response", "").strip()

        # Extract themes using multiple strategies
        themes = extract_themes_from_response(text)

        if not themes:
            print(f"Warning: Could not extract themes from response for {ticker}")
            print(f"Raw response: {text[:200]}...")
            return "error_parsing_themes"

        return themes

    except Exception as e:
        print(f"Error generating themes with Ollama: {e}")
        return "error_generating_themes"

def extract_themes_from_response(text: str) -> str:
    """Extract themes from Qwen2.5 response using multiple strategies."""

    # Strategy 1: Look for "THEMES:" format
    themes_match = re.search(r'THEMES:\s*([^\\n]+)', text, re.IGNORECASE)
    if themes_match:
        themes_text = themes_match.group(1).strip()
        return clean_themes(themes_text)

    # Strategy 2: Look for final line with comma-separated values
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    for line in reversed(lines):
        if ',' in line and len(line.split(',')) >= 2:
            # Check if it looks like themes (no sentences, mostly single words)
            words = line.split(',')
            if all(len(word.strip().split()) <= 2 for word in words):  # Max 2 words per theme
                return clean_themes(line)

    # Strategy 3: Look for lines that end the response and contain themes
    for line in reversed(lines):
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in ['themes are:', 'themes:', 'final themes:', 'selected themes:']):
            # Extract themes from this line
            colon_split = line.split(':')
            if len(colon_split) > 1:
                return clean_themes(colon_split[-1])

    # Strategy 4: Look for any line with 2-5 comma-separated single words
    for line in lines:
        if ',' in line:
            parts = line.split(',')
            if 2 <= len(parts) <= 5:
                if all(len(part.strip().split()) <= 2 for part in parts):
                    return clean_themes(line)

    return ""

def clean_themes(themes_text: str) -> str:
    """Clean and validate extracted themes."""
    if not themes_text:
        return ""

    # Basic cleaning
    themes_text = themes_text.strip().lower()
    themes_text = re.sub(r'[`"\'\\n\\r]', '', themes_text)

    # Split and clean individual themes
    themes = [t.strip() for t in themes_text.split(',') if t.strip()]

    # Filter out bad themes
    bad_themes = {
        'business', 'technology', 'company', 'industrial', 'economics',
        'growth', 'value', 'smallcap', 'largecap', 'consumer', 'commercial',
        'market', 'financial', 'investment', 'portfolio', 'fund', 'etf',
        'stock', 'equity', 'corporate', 'enterprise', 'solutions', 'services'
    }

    # Filter and dedupe
    seen = set()
    cleaned_themes = []

    for theme in themes:
        theme = theme.strip('.,()[]{}')
        if (theme and
            len(theme) > 1 and
            theme not in bad_themes and
            theme not in seen and
            not theme.isdigit() and
            len(theme.split()) <= 2):  # Max 2 words per theme
            seen.add(theme)
            cleaned_themes.append(theme)

    # Return up to 5 themes
    return ", ".join(cleaned_themes[:5])

async def get_etf_holdings(session, ticker):
    """Get ETF holdings for a ticker."""
    try:
        result = await etfHoldingsETFDBdotCOM(session, ticker)
        if result is not None and hasattr(result, 'columns') and not result.empty:
            # Get the ETF names (second column, index 1)
            if len(result.columns) > 1:
                etf_values = result.iloc[:, 1].tolist()
                return etf_values
        return []
    except Exception as e:
        print(f"Error getting ETF holdings for {ticker}: {e}")
        return []

async def process_screener_tickers(use_simple: bool = False):
    """Process tickers from screener file and generate themes."""

    # Load screener data
    screener_file = 'src/data/screener/filtered_sorted_screener_2025-09-24.xlsx'
    try:
        df = pd.read_excel(screener_file)
        print(f"Loaded {len(df)} rows from screener file")
        print(f"Columns: {df.columns.tolist()}")
    except Exception as e:
        print(f"Error loading screener file: {e}")
        return

    # Check if required columns exist
    if 'Symbol' not in df.columns or 'Description' not in df.columns:
        print("Required columns 'Symbol' and 'Description' not found in screener file")
        return

    # Process each ticker
    results = []
    prompt_type = "simple" if use_simple else "detailed"
    print(f"Using {prompt_type} prompt format")

    async with aiohttp.ClientSession() as session:
        for idx, row in df.iterrows():
            ticker = row['Symbol']
            description = row['Description']
            sector = row.get('Sector', '')
            industry = row.get('Industry', '')
            sub_industry = row.get('Sub-Industry', '')

            print(f"\nProcessing {ticker} ({idx+1}/{len(df)})")

            # Get ETF holdings
            etf_names = await get_etf_holdings(session, ticker)

            if etf_names:
                print(f"Found {len(etf_names)} ETF holdings for {ticker}")
                print(f"ETFs: {', '.join(etf_names[:3])}{'...' if len(etf_names) > 3 else ''}")

                # Generate themes using Ollama
                themes = generate_themes(ticker, description, etf_names, sector, industry, sub_industry, use_simple)
                print(f"Generated themes: {themes}")

                results.append({
                    'Ticker': ticker,
                    'Description': description,
                    'Sector': sector,
                    'Industry': industry,
                    'Sub_Industry': sub_industry,
                    'ETF_Count': len(etf_names),
                    'ETF_Names': '; '.join(etf_names),
                    'Generated_Themes': themes
                })
            else:
                print(f"No ETF holdings found for {ticker}")
                themes = generate_themes(ticker, description, [], sector, industry, sub_industry, use_simple)
                print(f"Generated themes: {themes}")

                results.append({
                    'Ticker': ticker,
                    'Description': description,
                    'Sector': sector,
                    'Industry': industry,
                    'Sub_Industry': sub_industry,
                    'ETF_Count': 0,
                    'ETF_Names': '',
                    'Generated_Themes': themes
                })

    # Save results
    if results:
        results_df = pd.DataFrame(results)
        output_file = f'ticker_themes_qwen25_{prompt_type}_{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}.csv'
        results_df.to_csv(output_file, index=False)
        print(f"\nResults saved to: {output_file}")
        print(f"Processed {len(results)} tickers")

        # Show summary
        with_etf = len([r for r in results if r['ETF_Count'] > 0])
        without_etf = len(results) - with_etf
        error_count = len([r for r in results if 'error' in r['Generated_Themes']])

        print(f"Tickers with ETF data: {with_etf}")
        print(f"Tickers without ETF data: {without_etf}")
        print(f"Theme generation errors: {error_count}")

        # Show sample themes
        print("\nSample generated themes:")
        for i, result in enumerate(results[:5]):
            print(f"  {result['Ticker']}: {result['Generated_Themes']}")
    else:
        print("No results to save")

async def test_single_ticker(ticker="OKLO", use_simple=False):
    """Test with a single ticker for debugging."""

    # Load screener data to get description
    screener_file = 'src/data/screener/filtered_sorted_screener_2025-09-24.xlsx'
    try:
        df = pd.read_excel(screener_file)
        ticker_row = df[df['Symbol'] == ticker]
        if ticker_row.empty:
            print(f"Ticker {ticker} not found in screener file")
            description = f"{ticker} - Test description"
            sector, industry, sub_industry = "N/A", "N/A", "N/A"
        else:
            description = ticker_row['Description'].iloc[0]
            sector = ticker_row.get('Sector', 'N/A').iloc[0]
            industry = ticker_row.get('Industry', 'N/A').iloc[0]
            sub_industry = ticker_row.get('Sub-Industry', 'N/A').iloc[0]
    except Exception as e:
        print(f"Error loading screener file: {e}")
        description = f"{ticker} - Test description"
        sector, industry, sub_industry = "N/A", "N/A", "N/A"

    print(f"Testing with {ticker}")
    print(f"Description: {description[:200]}...")
    print(f"Using {'simple' if use_simple else 'detailed'} prompt")

    async with aiohttp.ClientSession() as session:
        # Get ETF holdings
        etf_names = await get_etf_holdings(session, ticker)

        if etf_names:
            print(f"\nFound {len(etf_names)} ETF holdings:")
            for etf in etf_names[:5]:  # Show first 5
                print(f"  - {etf}")
            if len(etf_names) > 5:
                print(f"  ... and {len(etf_names) - 5} more")

            # Generate themes
            themes = generate_themes(ticker, description, etf_names, sector, industry, sub_industry, use_simple)
            print(f"\nGenerated themes: {themes}")
        else:
            themes = generate_themes(ticker, description, [], sector, industry, sub_industry, use_simple)
            print(f"No ETF holdings found for {ticker}")
            print(f"\nGenerated themes: {themes}")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--test", help="Test with single ticker", default=None)
    parser.add_argument("--all", action="store_true", help="Process all tickers from screener")
    parser.add_argument("--simple", action="store_true", help="Use simple prompt format (faster)")
    args = parser.parse_args()

    if args.test:
        asyncio.run(test_single_ticker(args.test, args.simple))
    elif args.all:
        asyncio.run(process_screener_tickers(args.simple))
    else:
        # Default: test with HIMS
        asyncio.run(test_single_ticker("HIMS", args.simple))