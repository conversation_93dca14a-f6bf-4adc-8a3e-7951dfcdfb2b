import requests
import json
from dotenv import load_dotenv
import os

# Load API key
load_dotenv()
API_KEY = os.getenv('FINNHUB_API_KEY')
BASE_URL = "https://finnhub.io/api/v1"

def safe_get(url, params):
    """Helper to safely call API and return JSON or {}"""
    try:
        r = requests.get(url, params=params, timeout=10)
        r.raise_for_status()
        return r.json()
    except Exception as e:
        print(f"Error fetching {url}: {e}")
        return {}

# Fetch US stock symbols (limit for demo)
symbols = safe_get(f"{BASE_URL}/stock/symbol", {"exchange": "US", "token": API_KEY})
symbols = symbols[:10]  # limit to 50 to avoid overloading API

dataset = []
for sym in symbols:
    symbol = sym.get("symbol")
    if not symbol:
        continue

    # Get company profile
    profile = safe_get(f"{BASE_URL}/stock/profile2", {"symbol": symbol, "token": API_KEY})
    if not profile:
        continue

    name = profile.get("name", symbol)
    description = profile.get("description") or f"{name} company overview."
    print(f"Processing {symbol}: {name}",profile)
    theme = profile.get("sector") or "General"

    # Build training example
    example = {
        "messages": [
            {"role": "user", "content": f"Classify the following stock description into themes (comma-separated): {description}"},
            {"role": "assistant", "content": theme}
        ]
    }
    dataset.append(example)

# Save to JSON
with open("stock_themes.json", "w") as f:
    json.dump(dataset, f, indent=2)

print(f"Generated {len(dataset)} examples")
