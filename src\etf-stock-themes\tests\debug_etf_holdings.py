#!/usr/bin/env python3
"""
Debug what etfHoldingsETFDBdotCOM returns
"""

import sys
import asyncio
import aiohttp

# Add src to path to import scrapper functions
sys.path.append('src')
from util.scrapper import etfHoldingsETFDBdotCOM

async def debug_holdings():
    """Debug what the holdings function returns"""
    
    async with aiohttp.ClientSession() as session:
        print("Getting ETFs that hold OKLO...")
        df = await etfHoldingsETFDBdotCOM(session, "OKLO")
        
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {df.columns.tolist()}")
        print(f"DataFrame index: {df.index.tolist()}")
        print("\nFirst few rows:")
        print(df.head())
        print("\nDataFrame info:")
        print(df.info())

if __name__ == "__main__":
    asyncio.run(debug_holdings())
