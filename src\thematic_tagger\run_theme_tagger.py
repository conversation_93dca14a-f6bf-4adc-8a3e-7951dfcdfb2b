#!/usr/bin/env python3
"""
Simple runner script for the theme tagger.
Run this from the project root directory.
"""

import asyncio
import sys
import os

# Add src to path so we can import modules
sys.path.insert(0, 'src')

from theme_tagger import main

if __name__ == "__main__":
    print("Starting Theme Tagger...")
    print("This will process all tickers in src/data/liked_tickers.txt")
    print("and create a CSV file with theme assignments.\n")
    
    # Check if the ticker file exists
    if not os.path.exists('src/data/liked_tickers.txt'):
        print("Error: src/data/liked_tickers.txt not found!")
        print("Please make sure you're running this from the project root directory.")
        sys.exit(1)
    
    try:
        asyncio.run(main())
        print("\nTheme tagging completed successfully!")
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
