import datetime
import os
import aiohttp
import pandas as pd
from stockdata.data_source import getStockDataV3

async def process_ticker(session: aiohttp.ClientSession, row, doc, save_doc,daily_id, wkly_id):
    xyz = row['Symbol']
    df = await getStockDataV3(session, xyz)

    # Helper function to safely get numeric value (handles multi-level columns)
    def safe_get_value(column_name):
        if df is None or df.empty:
            return None

        # Try direct column access first
        if column_name in df.columns:
            raw_value = df[column_name].iloc[0]
            if pd.notna(raw_value):
                return float(raw_value)

        # Try multi-level column access (for data like ('Close', 'AAOI'))
        for col in df.columns:
            if isinstance(col, tuple) and len(col) >= 1 and col[0] == column_name:
                raw_value = df[col].iloc[0]
                if pd.notna(raw_value):
                    return float(raw_value)

        return None

    try:
        conditions_met = False
        if df is not None and df.size > 0:
            # Get all values safely
            slope_50 = safe_get_value('slope_50')
            slope_200 = safe_get_value('slope_200')
            ud_ratio = safe_get_value('UDRatio')
            ma_150 = safe_get_value('150')
            ma_200 = safe_get_value('200')
            ma_50 = safe_get_value('50')
            volume_m = safe_get_value('$VolumeM')
            rs_rating = safe_get_value('RS_Rating') if 'RS_Rating' in df.columns else None

            # Check all conditions with proper null handling
            conditions_met = (
                slope_50 is not None and slope_50 > 0
                and slope_200 is not None and slope_200 > 0
                and ud_ratio is not None and ud_ratio > 0.75
                and ma_150 is not None and ma_200 is not None and ma_150 > ma_200
                and ma_50 is not None and ma_150 is not None and ma_50 > ma_150
                and ma_50 is not None and ma_200 is not None and ma_50 > ma_200
                and volume_m is not None and volume_m >= 25
                and rs_rating is not None and rs_rating > 80
            )
    except Exception as e:
        print(f"Error in process_ticker for {xyz}: {e}")
        conditions_met = False

    if conditions_met:
        if save_doc:
            # This part is now handled by docx_generator
            pass

    else:
        return xyz, determine_exclusion_reasons(df) , df
    return None, None , df

def determine_exclusion_reasons(df):
    reasons = []
    if df is None:
       reasons.append("DataFrame is None")
       return ', '.join(reasons)
    if df.size == 0:
        reasons.append("DataFrame is empty")
        return ', '.join(reasons)

    try:
        # Helper function to safely get numeric value (handles multi-level columns)
        def safe_get_value(column_name):
            if df is None or df.empty:
                return None

            # Try direct column access first
            if column_name in df.columns:
                raw_value = df[column_name].iloc[0]
                if pd.notna(raw_value):
                    return float(raw_value)

            # Try multi-level column access (for data like ('Close', 'AAOI'))
            for col in df.columns:
                if isinstance(col, tuple) and len(col) >= 1 and col[0] == column_name:
                    raw_value = df[col].iloc[0]
                    if pd.notna(raw_value):
                        return float(raw_value)

            return None

        # Check slope_50
        slope_50 = safe_get_value('slope_50')
        if slope_50 is None or slope_50 <= 0:
            reasons.append("slope_50 not > 0")

        # Check slope_200
        slope_200 = safe_get_value('slope_200')
        if slope_200 is None or slope_200 <= 0:
            reasons.append("slope_200 not > 0")

        # Check UDRatio
        ud_ratio = safe_get_value('UDRatio')
        if ud_ratio is None or ud_ratio <= 0.75:
            reasons.append("UDRatio not > 0.75")

        # Check MA relationships
        ma_150 = safe_get_value('150')
        ma_200 = safe_get_value('200')
        ma_50 = safe_get_value('50')

        if ma_150 is None or ma_200 is None or ma_150 <= ma_200:
            reasons.append("150 not > 200")

        if ma_50 is None or ma_150 is None or ma_50 <= ma_150:
            reasons.append("50 not > 150")

        if ma_50 is None or ma_200 is None or ma_50 <= ma_200:
            reasons.append("50 not > 200")

        # Check volume
        volume_m = safe_get_value('$VolumeM')
        if volume_m is None or volume_m < 25:
            reasons.append("$VolumeM not > 25")

        # Check RS_Rating
        if 'RS_Rating' in df.columns:
            rs_rating = safe_get_value('RS_Rating')
            if rs_rating is None or rs_rating <= 80:
                reasons.append("RS_Rating not > 80")
        else:
            reasons.append("RS_Rating not available")

    except Exception as e:
        reasons.append(f"Error in exclusion check: {e}")

    return ', '.join(reasons)



def save_filtered_screener(screenerdf, excluded_tickers):
    excluded_symbols = [ticker for ticker, _ in excluded_tickers]
    df = screenerdf.loc[~screenerdf['Symbol'].isin(excluded_symbols)].copy()

    # Restore original sorting logic (pre-Signal_Score): sort by sector/industry groupings and Percentile
    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')

    # Sort by sector/industry groupings, then by Percentile (descending)
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Percentile'],
                       ascending=[False, False, False, False])

    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)
    df.drop(columns=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], inplace=True)

    # Add descriptions to the dataframe
    try:
        from .description_utils import add_descriptions_sync
        df = add_descriptions_sync(df)
    except Exception as e:
        print(f"⚠️ Warning: Could not add descriptions: {e}")

    screener_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'src/data', 'screener')
    os.makedirs(screener_dir, exist_ok=True)
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_name = f"filtered_sorted_screener_{current_date}.xlsx"
    file_path = os.path.join(screener_dir, file_name)
    df.to_excel(file_path)
    print(f"Saved filtered screener to {file_path}")

    # Print top signals summary
    print("\n=== TOP SIGNAL STOCKS ===")
    top_signals = df.head(10)[['Symbol', 'Signal_Score', 'Signal', 'Company Name', 'Sector']]
    for _, row in top_signals.iterrows():
        print(f"{row['Symbol']:6} | {row['Signal_Score']:5.1f} | {row['Signal']:12} | {row['Company Name'][:30]:30} | {row['Sector']}")

    return df

def calculate_signal_score(row, df):
    """
    Calculate a comprehensive signal score for a stock based on multiple criteria.
    Returns a score from 0-100 where higher is better.
    """
    score = 0
    max_score = 100

    # Technical Analysis Criteria (40 points max)
    technical_score = 0

    # Helper function to safely access columns (handles multi-level column names)
    def safe_get_column_value(column_name, convert_to_float=True):
        if df is None or df.empty:
            return None

        try:
            # Try direct column access first
            if column_name in df.columns:
                raw_value = df[column_name].iloc[0]
                # Ensure we get a scalar value, not a Series
                if hasattr(raw_value, 'iloc'):
                    raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
                if pd.notna(raw_value):
                    return float(raw_value) if convert_to_float else raw_value

            # Try multi-level column access (for data like ('Close', 'AAOI'))
            for col in df.columns:
                if isinstance(col, tuple) and len(col) >= 1 and col[0] == column_name:
                    raw_value = df[col].iloc[0]
                    # Ensure we get a scalar value, not a Series
                    if hasattr(raw_value, 'iloc'):
                        raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
                    if pd.notna(raw_value):
                        return float(raw_value) if convert_to_float else raw_value
        except Exception as e:
            # If there's any issue with column access, return None
            print(f"Debug: Column access error for '{column_name}': {e}")
            return None

        return None

    # 1. Price above all major MAs (15 points)
    if df is not None and not df.empty:
        try:
            current_price = safe_get_column_value('Close')

            if current_price is not None and current_price > 0:
                ma_score = 0

                # Check if price is above each MA
                mas_to_check = ['50', '100', '150', '200']
                for ma in mas_to_check:
                    ma_value = safe_get_column_value(ma)
                    if ma_value is not None and current_price > ma_value:
                        ma_score += 3.75  # 15 points / 4 MAs

                technical_score += ma_score

                # 2. TTM Squeeze (10 points)
                try:
                    ttm_value = safe_get_column_value('TTM', convert_to_float=False)  # Keep original type
                    if ttm_value is not None:
                        # Handle both boolean and numeric TTM values
                        if isinstance(ttm_value, bool):
                            ttm_active = ttm_value
                        elif isinstance(ttm_value, (int, float)):
                            ttm_active = bool(ttm_value)
                        else:
                            # Handle string or other types
                            ttm_active = str(ttm_value).lower() in ['true', '1', 'yes']

                        if ttm_active:
                            technical_score += 10
                except Exception as ttm_error:
                    # This might be where the Series error is coming from
                    if "truth value of a Series is ambiguous" in str(ttm_error):
                        print(f"Warning: TTM Series comparison error for {row.get('Symbol', 'Unknown')}: {ttm_error}")
                    else:
                        print(f"Warning: TTM value processing error for {row.get('Symbol', 'Unknown')}: {ttm_error}")
                    # Skip TTM scoring if there's an issue

                # 3. Strong trend (slopes positive) (10 points)
                slope_50 = safe_get_column_value('slope_50')
                slope_200 = safe_get_column_value('slope_200')

                if slope_50 is not None and slope_200 is not None:
                    if slope_50 > 0 and slope_200 > 0:
                        technical_score += 10

                # 4. Volume strength (5 points)
                ud_ratio = safe_get_column_value('UDRatio')
                if ud_ratio is not None and ud_ratio > 0.75:
                    technical_score += 5

        except Exception as e:
            # More specific error handling to identify the exact issue
            error_msg = str(e)
            symbol = row.get('Symbol', 'Unknown')

            if "truth value of a Series is ambiguous" in error_msg:
                print(f"Warning: Series comparison error in technical analysis for {symbol}: {e}")
                print(f"  This suggests a pandas Series is being used in a boolean context")
                print(f"  DataFrame columns: {list(df.columns) if df is not None else 'None'}")
            else:
                print(f"Warning: Error in technical analysis calculation for {symbol}: {e}")
            # Continue with score = 0 for technical analysis

    score += technical_score

    # Volume Criteria (15 points max)
    volume_score = 0

    # 1. High dollar volume (10 points)
    if df is not None and not df.empty:
        try:
            volume_m = safe_get_column_value('$VolumeM')
            if volume_m is not None:
                if volume_m >= 50:  # $50M+ daily volume
                    volume_score += 10
                elif volume_m >= 25:  # $25M+ daily volume
                    volume_score += 7
                elif volume_m >= 10:  # $10M+ daily volume
                    volume_score += 4
        except (ValueError, TypeError, IndexError, KeyError):
            pass

    # 2. Volume from screener data (5 points)
    try:
        if hasattr(row, 'index') and '$Volume' in row.index and pd.notna(row['$Volume']):
            daily_volume_raw = row['$Volume']
            if pd.notna(daily_volume_raw):
                daily_volume = float(daily_volume_raw)
                if daily_volume >= 50:  # $50M+ from screener
                    volume_score += 5
                elif daily_volume >= 25:  # $25M+ from screener
                    volume_score += 3
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    score += volume_score

    # Fundamental Criteria (25 points max)
    fundamental_score = 0

    # 1. EPS Growth (15 points total)
    eps_criteria = [
        ('EPS Growth (TTM vs Prior TTM)', 5),
        ('EPS Growth (Last Qtr vs. Same Qtr Prior Yr)', 5),
        ('EPS Growth (Proj Next Yr vs. This Yr)', 5)
    ]

    for eps_col, points in eps_criteria:
        try:
            if (hasattr(row, 'index') and eps_col in row.index and
                pd.notna(row[eps_col])):
                eps_growth_raw = row[eps_col]
                if pd.notna(eps_growth_raw):
                    eps_growth = float(eps_growth_raw)
                    if eps_growth >= 100:  # Triple digit growth
                        fundamental_score += points
                    elif eps_growth >= 50:  # Double digit high growth
                        fundamental_score += points * 0.8
                    elif eps_growth >= 25:  # Strong growth
                        fundamental_score += points * 0.6
                    elif eps_growth >= 10:  # Decent growth
                        fundamental_score += points * 0.4
        except (ValueError, TypeError, KeyError, AttributeError):
            continue

    # 2. Revenue Growth (5 points)
    revenue_col = 'Revenue Growth (TTM vs. Prior TTM)'
    try:
        if (hasattr(row, 'index') and revenue_col in row.index and
            pd.notna(row[revenue_col])):
            revenue_growth_raw = row[revenue_col]
            if pd.notna(revenue_growth_raw):
                revenue_growth = float(revenue_growth_raw)
                if revenue_growth >= 25:
                    fundamental_score += 5
                elif revenue_growth >= 15:
                    fundamental_score += 3
                elif revenue_growth >= 10:
                    fundamental_score += 2
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 3. Research Rating (5 points)
    ess_col = 'Equity Summary Score (ESS) from LSEG StarMine'
    try:
        if (hasattr(row, 'index') and ess_col in row.index and
            pd.notna(row[ess_col])):
            ess_score_raw = row[ess_col]
            if pd.notna(ess_score_raw):
                ess_score = float(ess_score_raw)
                if ess_score >= 8:
                    fundamental_score += 5
                elif ess_score >= 6:
                    fundamental_score += 3
                elif ess_score >= 4:
                    fundamental_score += 1
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    score += fundamental_score

    # Momentum Criteria (20 points max)
    momentum_score = 0

    # 1. RS Rating (10 points)
    try:
        if (hasattr(row, 'index') and 'RS_Rating' in row.index and
            pd.notna(row['RS_Rating'])):
            rs_rating_raw = row['RS_Rating']
            if pd.notna(rs_rating_raw):
                rs_rating = float(rs_rating_raw)
                if rs_rating >= 90:
                    momentum_score += 10
                elif rs_rating >= 80:
                    momentum_score += 8
                elif rs_rating >= 70:
                    momentum_score += 6
                elif rs_rating >= 60:
                    momentum_score += 4
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 2. Price Performance (10 points total)
    perf_criteria = [
        ('Price Performance (4 Weeks)', 3),
        ('Price Performance (13 Weeks)', 4),
        ('Price Performance (52 Weeks)', 3)
    ]

    for perf_col, points in perf_criteria:
        try:
            if (hasattr(row, 'index') and perf_col in row.index and
                pd.notna(row[perf_col])):
                performance_raw = row[perf_col]
                if pd.notna(performance_raw):
                    performance = float(performance_raw)
                    if performance >= 50:  # Strong performance
                        momentum_score += points
                    elif performance >= 25:  # Good performance
                        momentum_score += points * 0.7
                    elif performance >= 10:  # Decent performance
                        momentum_score += points * 0.4
        except (ValueError, TypeError, KeyError, AttributeError):
            continue

    score += momentum_score

    # Ensure score doesn't exceed 100
    return min(score, max_score)


def calculate_signal_score_equal_weight(row, df):
    """
    Calculate signal score using equal weights for each criterion.
    Each criterion gets equal points (10 points each) for a total of 100 points.

    Criteria (10 points each):
    1. 🚀 Exceptional RS Rating (90+)
    2. 📈 100%+ Annual Gain
    3. 🏢 Exceptional Revenue Growth (25%+)
    4. 🌊 High Volume ($1B+ annual / $50M+ daily)
    5. 💪 Strong EPS Growth (25%+ TTM)
    6. 📊 Price Above All MAs (50, 100, 150, 200)
    7. 🎯 TTM Squeeze Active
    8. 📈 Strong Trend (Positive Slopes)
    9. 🔥 Strong Recent Performance (50%+ 13-week)
    10. ⭐ High Research Rating (ESS 8+)
    """
    score = 0
    points_per_criterion = 10

    # Helper function to safely access columns
    def safe_get_column_value(column_name, convert_to_float=True):
        if df is None or df.empty:
            return None
        try:
            if column_name in df.columns:
                raw_value = df[column_name].iloc[0]
                if hasattr(raw_value, 'iloc'):
                    raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
                if pd.notna(raw_value):
                    return float(raw_value) if convert_to_float else raw_value
            for col in df.columns:
                if isinstance(col, tuple) and len(col) >= 1 and col[0] == column_name:
                    raw_value = df[col].iloc[0]
                    if hasattr(raw_value, 'iloc'):
                        raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
                    if pd.notna(raw_value):
                        return float(raw_value) if convert_to_float else raw_value
        except Exception:
            return None
        return None

    # 1. 🚀 Exceptional RS Rating (90+) - 10 points
    try:
        if hasattr(row, 'index') and 'RS_Rating' in row.index and pd.notna(row['RS_Rating']):
            rs_rating = float(row['RS_Rating'])
            if rs_rating >= 90:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 2. 📈 100%+ Annual Gain - 10 points
    try:
        if hasattr(row, 'index') and 'Price Performance (52 Weeks)' in row.index:
            annual_gain = float(row['Price Performance (52 Weeks)'])
            if annual_gain >= 100:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 3. 🏢 Exceptional Revenue Growth (25%+) - 10 points
    try:
        revenue_col = 'Revenue Growth (TTM vs. Prior TTM)'
        if hasattr(row, 'index') and revenue_col in row.index and pd.notna(row[revenue_col]):
            revenue_growth = float(row[revenue_col])
            if revenue_growth >= 25:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 4. 🌊 High Volume ($50M+ daily) - 10 points
    volume_qualified = False
    try:
        # Check daily volume from technical data
        volume_m = safe_get_column_value('$VolumeM')
        if volume_m is not None and volume_m >= 50:
            volume_qualified = True

        # Also check screener volume data
        if not volume_qualified and hasattr(row, 'index') and '$Volume' in row.index:
            daily_volume = float(row['$Volume'])
            if daily_volume >= 50:
                volume_qualified = True

        if volume_qualified:
            score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 5. 💪 Strong EPS Growth (25%+ TTM) - 10 points
    try:
        eps_col = 'EPS Growth (TTM vs Prior TTM)'
        if hasattr(row, 'index') and eps_col in row.index and pd.notna(row[eps_col]):
            eps_growth = float(row[eps_col])
            if eps_growth >= 25:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 6. 📊 Price Above All MAs (50, 100, 150, 200) - 10 points
    if df is not None and not df.empty:
        try:
            current_price = safe_get_column_value('Close')
            if current_price is not None and current_price > 0:
                mas_to_check = ['50', '100', '150', '200']
                above_all_mas = True
                for ma in mas_to_check:
                    ma_value = safe_get_column_value(ma)
                    if ma_value is None or current_price <= ma_value:
                        above_all_mas = False
                        break
                if above_all_mas:
                    score += points_per_criterion
        except Exception:
            pass

    # 7. 🎯 TTM Squeeze Active - 10 points
    try:
        ttm_value = safe_get_column_value('TTM', convert_to_float=False)
        if ttm_value is not None:
            if isinstance(ttm_value, bool):
                ttm_active = ttm_value
            elif isinstance(ttm_value, (int, float)):
                ttm_active = bool(ttm_value)
            else:
                ttm_active = str(ttm_value).lower() in ['true', '1', 'yes']

            if ttm_active:
                score += points_per_criterion
    except Exception:
        pass

    # 8. 📈 Strong Trend (Positive Slopes) - 10 points
    try:
        slope_50 = safe_get_column_value('slope_50')
        slope_200 = safe_get_column_value('slope_200')
        if slope_50 is not None and slope_200 is not None:
            if slope_50 > 0 and slope_200 > 0:
                score += points_per_criterion
    except Exception:
        pass

    # 9. 🔥 Strong Recent Performance (50%+ 13-week) - 10 points
    try:
        if hasattr(row, 'index') and 'Price Performance (13 Weeks)' in row.index:
            recent_perf = float(row['Price Performance (13 Weeks)'])
            if recent_perf >= 50:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    # 10. ⭐ High Research Rating (ESS 8+) - 10 points
    try:
        ess_col = 'Equity Summary Score (ESS) from LSEG StarMine'
        if hasattr(row, 'index') and ess_col in row.index and pd.notna(row[ess_col]):
            ess_score = float(row[ess_col])
            if ess_score >= 8:
                score += points_per_criterion
    except (ValueError, TypeError, KeyError, AttributeError):
        pass

    return min(score, 100)  # Cap at 100


# Momentum + Earnings Acceleration + High RS Scoring System (v3)
SCORER_CFG = {
    "weights": {
        "eps_lq": 13, "eps_ttm": 12, "eps_ny": 10,
        "rs": 20, "p13": 10, "p52": 5,
        "ma_block": 10, "slope_50": 2.5, "slope_200": 2.5, "ttm": 5,
        "volM": 7, "ud": 3,
    },
    "scales": {
        "eps_lq": 100, "eps_ttm": 100, "eps_ny": 50,
        "rs_floor": 70, "rs_span": 30,
        "p13_full": 0.50, "p52_full": 1.00,
        "ma_full": 0.10,
        "volM_zero": 10, "volM_full_delta": 40,
        "ud_zero": 1.05, "ud_full_delta": 0.25,
    },
    "ma_aliases": [
        ["50", "SMA_50", "MA50"],
        ["100", "SMA_100", "MA100"],
        ["150", "SMA_150", "MA150"],
        ["200", "SMA_200", "MA200"]
    ],
    "ttm_truthy": {"1", "true", "yes", "y"}
}


def _latest_value(df, name, to_float=True):
    """
    Get latest value from df (iloc[0]) supporting both single and multi-index columns.
    """
    if df is None or df.empty:
        return None

    try:
        # Try direct column access first
        if name in df.columns:
            raw_value = df[name].iloc[0]
            if hasattr(raw_value, 'iloc'):
                raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
            if pd.notna(raw_value):
                return float(raw_value) if to_float else raw_value

        # Try multi-index column access
        for col in df.columns:
            if isinstance(col, tuple) and len(col) >= 1 and col[0] == name:
                raw_value = df[col].iloc[0]
                if hasattr(raw_value, 'iloc'):
                    raw_value = raw_value.iloc[0] if len(raw_value) > 0 else None
                if pd.notna(raw_value):
                    return float(raw_value) if to_float else raw_value
    except Exception:
        return None

    return None


def _rget(row, name):
    """
    Safely get float value from row, return None if missing/invalid.
    """
    try:
        if hasattr(row, 'index') and name in row.index and pd.notna(row[name]):
            return float(row[name])
    except (ValueError, TypeError, KeyError, AttributeError):
        pass
    return None


def _clip(x):
    """Clip value to [0, 1] range."""
    return min(max(x, 0), 1)


def calculate_signal_score_v3(row, df):
    """
    Momentum + Earnings Acceleration + High RS Scoring System (v3)

    Total = 100 points with continuous scaling:
    - Earnings Acceleration: 35 pts
    - Momentum & RS: 35 pts
    - Trend & Setup Quality: 20 pts
    - Liquidity & Accumulation: 10 pts

    Returns score in [0, 100] range.
    """
    cfg = SCORER_CFG
    weights = cfg["weights"]
    scales = cfg["scales"]

    total_score = 0.0

    # A) Earnings Acceleration — 35 pts
    earnings_score = 0.0

    # Last-Qtr EPS YoY (13 pts)
    eps_lq = _rget(row, 'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)')
    if eps_lq is not None:
        earnings_score += weights["eps_lq"] * _clip(max(0, eps_lq) / scales["eps_lq"])

    # TTM EPS YoY (12 pts)
    eps_ttm = _rget(row, 'EPS Growth (TTM vs Prior TTM)')
    if eps_ttm is not None:
        earnings_score += weights["eps_ttm"] * _clip(max(0, eps_ttm) / scales["eps_ttm"])

    # Next-Year vs This-Year EPS (10 pts)
    eps_ny = _rget(row, 'EPS Growth (Proj Next Yr vs. This Yr)')
    if eps_ny is not None:
        earnings_score += weights["eps_ny"] * _clip(max(0, eps_ny) / scales["eps_ny"])

    total_score += earnings_score

    # B) Momentum & RS — 35 pts
    momentum_score = 0.0

    # RS Rating (20 pts) - 0 at RS≤70; full by RS≥100
    rs_rating = _rget(row, 'RS_Rating')
    if rs_rating is not None:
        momentum_score += weights["rs"] * _clip((rs_rating - scales["rs_floor"]) / scales["rs_span"])

    # 13-Week Performance (10 pts) - full at +50%
    p13 = _rget(row, 'Price Performance (13 Weeks)')
    if p13 is not None:
        momentum_score += weights["p13"] * _clip((p13/100) / scales["p13_full"])

    # 52-Week Performance (5 pts) - full at +100%
    p52 = _rget(row, 'Price Performance (52 Weeks)')
    if p52 is not None:
        momentum_score += weights["p52"] * _clip((p52/100) / scales["p52_full"])

    total_score += momentum_score

    # C) Trend & Setup Quality — 20 pts
    trend_score = 0.0

    # Distance Above MAs (10 pts total)
    if df is not None and not df.empty:
        current_price = _latest_value(df, 'Close')
        if current_price is not None and current_price > 0:
            ma_scores = []

            # Check each MA group for aliases
            for ma_group in cfg["ma_aliases"]:
                ma_value = None
                for ma_name in ma_group:
                    ma_value = _latest_value(df, ma_name)
                    if ma_value is not None:
                        break

                if ma_value is not None and ma_value > 0:
                    # Full score if ≥ +10% above MA
                    ma_distance_score = _clip((current_price/ma_value - 1) / scales["ma_full"])
                    ma_scores.append(ma_distance_score)

            if ma_scores:
                avg_ma_score = sum(ma_scores) / len(ma_scores)
                trend_score += weights["ma_block"] * avg_ma_score

    # Positive Slopes (5 pts total)
    slope_50 = _latest_value(df, 'slope_50')
    if slope_50 is not None and slope_50 > 0:
        trend_score += weights["slope_50"]

    slope_200 = _latest_value(df, 'slope_200')
    if slope_200 is not None and slope_200 > 0:
        trend_score += weights["slope_200"]

    # TTM Squeeze (5 pts)
    ttm_value = _latest_value(df, 'TTM', to_float=False)
    if ttm_value is not None:
        ttm_active = False
        if isinstance(ttm_value, bool):
            ttm_active = ttm_value
        elif isinstance(ttm_value, (int, float)):
            ttm_active = bool(ttm_value)
        else:
            ttm_active = str(ttm_value).lower() in cfg["ttm_truthy"]

        if ttm_active:
            trend_score += weights["ttm"]

    total_score += trend_score

    # D) Liquidity & Accumulation — 10 pts
    liquidity_score = 0.0

    # Dollar Volume (7 pts) - 0 at 10M, full at 50M+
    vol_m = _latest_value(df, '$VolumeM')
    if vol_m is None:
        # Try screener volume data
        vol_screener = _rget(row, '$Volume')
        if vol_screener is not None:
            vol_m = vol_screener

    if vol_m is not None:
        liquidity_score += weights["volM"] * _clip((vol_m - scales["volM_zero"]) / scales["volM_full_delta"])

    # Up/Down Ratio (3 pts) - full at 1.30+
    ud_ratio = _latest_value(df, 'UDRatio')
    if ud_ratio is not None:
        liquidity_score += weights["ud"] * _clip((ud_ratio - scales["ud_zero"]) / scales["ud_full_delta"])

    total_score += liquidity_score

    return min(total_score, 100.0)


def calculate_signal_score_v3_with_diagnostics(row, df):
    """
    Calculate v3 score with detailed diagnostics for analysis.
    Returns (score, diagnostics_dict).
    """
    cfg = SCORER_CFG
    weights = cfg["weights"]
    scales = cfg["scales"]

    # Get symbol
    symbol = row.get('Symbol', 'UNKNOWN') if hasattr(row, 'get') else 'UNKNOWN'

    # Initialize diagnostics
    diagnostics = {
        "symbol": symbol,
        "score": 0.0,
        "pillars": {
            "earnings": 0.0,
            "momentum": 0.0,
            "trend": 0.0,
            "liquidity": 0.0
        },
        "inputs": {}
    }

    total_score = 0.0

    # A) Earnings Acceleration — 35 pts
    earnings_score = 0.0

    eps_lq = _rget(row, 'EPS Growth (Last Qtr vs. Same Qtr Prior Yr)')
    diagnostics["inputs"]["EPS_LQ"] = eps_lq
    if eps_lq is not None:
        earnings_score += weights["eps_lq"] * _clip(max(0, eps_lq) / scales["eps_lq"])

    eps_ttm = _rget(row, 'EPS Growth (TTM vs Prior TTM)')
    diagnostics["inputs"]["EPS_TTM"] = eps_ttm
    if eps_ttm is not None:
        earnings_score += weights["eps_ttm"] * _clip(max(0, eps_ttm) / scales["eps_ttm"])

    eps_ny = _rget(row, 'EPS Growth (Proj Next Yr vs. This Yr)')
    diagnostics["inputs"]["EPS_NY"] = eps_ny
    if eps_ny is not None:
        earnings_score += weights["eps_ny"] * _clip(max(0, eps_ny) / scales["eps_ny"])

    diagnostics["pillars"]["earnings"] = earnings_score
    total_score += earnings_score

    # B) Momentum & RS — 35 pts
    momentum_score = 0.0

    rs_rating = _rget(row, 'RS_Rating')
    diagnostics["inputs"]["RS"] = rs_rating
    if rs_rating is not None:
        momentum_score += weights["rs"] * _clip((rs_rating - scales["rs_floor"]) / scales["rs_span"])

    p13 = _rget(row, 'Price Performance (13 Weeks)')
    diagnostics["inputs"]["P13"] = p13
    if p13 is not None:
        momentum_score += weights["p13"] * _clip((p13/100) / scales["p13_full"])

    p52 = _rget(row, 'Price Performance (52 Weeks)')
    diagnostics["inputs"]["P52"] = p52
    if p52 is not None:
        momentum_score += weights["p52"] * _clip((p52/100) / scales["p52_full"])

    diagnostics["pillars"]["momentum"] = momentum_score
    total_score += momentum_score

    # C) Trend & Setup Quality — 20 pts
    trend_score = 0.0

    # Get current price and MAs
    current_price = _latest_value(df, 'Close')
    diagnostics["inputs"]["Close"] = current_price

    # Get MA values
    for ma_group in cfg["ma_aliases"]:
        ma_key = f"MA{ma_group[0]}"  # MA50, MA100, etc.
        ma_value = None
        for ma_name in ma_group:
            ma_value = _latest_value(df, ma_name)
            if ma_value is not None:
                break
        diagnostics["inputs"][ma_key] = ma_value

    # Calculate MA distance score
    if current_price is not None and current_price > 0:
        ma_scores = []
        for ma_group in cfg["ma_aliases"]:
            ma_value = None
            for ma_name in ma_group:
                ma_value = _latest_value(df, ma_name)
                if ma_value is not None:
                    break

            if ma_value is not None and ma_value > 0:
                ma_distance_score = _clip((current_price/ma_value - 1) / scales["ma_full"])
                ma_scores.append(ma_distance_score)

        if ma_scores:
            avg_ma_score = sum(ma_scores) / len(ma_scores)
            trend_score += weights["ma_block"] * avg_ma_score

    # Slopes
    slope_50 = _latest_value(df, 'slope_50')
    slope_200 = _latest_value(df, 'slope_200')
    diagnostics["inputs"]["Slope50"] = slope_50
    diagnostics["inputs"]["Slope200"] = slope_200

    if slope_50 is not None and slope_50 > 0:
        trend_score += weights["slope_50"]
    if slope_200 is not None and slope_200 > 0:
        trend_score += weights["slope_200"]

    # TTM Squeeze
    ttm_value = _latest_value(df, 'TTM', to_float=False)
    ttm_active = False
    if ttm_value is not None:
        if isinstance(ttm_value, bool):
            ttm_active = ttm_value
        elif isinstance(ttm_value, (int, float)):
            ttm_active = bool(ttm_value)
        else:
            ttm_active = str(ttm_value).lower() in cfg["ttm_truthy"]

        if ttm_active:
            trend_score += weights["ttm"]

    diagnostics["inputs"]["TTM"] = ttm_active
    diagnostics["pillars"]["trend"] = trend_score
    total_score += trend_score

    # D) Liquidity & Accumulation — 10 pts
    liquidity_score = 0.0

    # Volume
    vol_m = _latest_value(df, '$VolumeM')
    if vol_m is None:
        vol_screener = _rget(row, '$Volume')
        if vol_screener is not None:
            vol_m = vol_screener

    diagnostics["inputs"]["VolM"] = vol_m
    if vol_m is not None:
        liquidity_score += weights["volM"] * _clip((vol_m - scales["volM_zero"]) / scales["volM_full_delta"])

    # Up/Down Ratio
    ud_ratio = _latest_value(df, 'UDRatio')
    diagnostics["inputs"]["UD"] = ud_ratio
    if ud_ratio is not None:
        liquidity_score += weights["ud"] * _clip((ud_ratio - scales["ud_zero"]) / scales["ud_full_delta"])

    diagnostics["pillars"]["liquidity"] = liquidity_score
    total_score += liquidity_score

    final_score = min(total_score, 100.0)
    diagnostics["score"] = final_score

    return final_score, diagnostics


def get_signal_strength(score):
    """Convert numerical score to descriptive signal strength"""
    if score >= 80:
        return "STRONG BUY"
    elif score >= 65:
        return "BUY"
    elif score >= 50:
        return "MODERATE BUY"
    elif score >= 35:
        return "HOLD"
    elif score >= 20:
        return "WEAK"
    else:
        return "AVOID"


async def filter_stocks(session: aiohttp.ClientSession, screenerdf):
    """
    Filter stocks based on technical criteria and return filtered dataframe and excluded tickers
    """
    excluded_tickers = []

    # Initialize signal column
    screenerdf['Signal_Score'] = 0.0
    screenerdf['Signal'] = "AVOID"

    for _, row in screenerdf.iterrows():
        xyz = row['Symbol']
        df = await getStockDataV3(session, xyz)

        if df is not None and df.size > 0:
            # Helper function to safely get numeric value (handles multi-level columns)
            def safe_get_value(column_name):
                if df is None or df.empty:
                    return None

                # Try direct column access first
                if column_name in df.columns:
                    raw_value = df[column_name].iloc[0]
                    if pd.notna(raw_value):
                        return float(raw_value)

                # Try multi-level column access (for data like ('Close', 'AAOI'))
                for col in df.columns:
                    if isinstance(col, tuple) and len(col) >= 1 and col[0] == column_name:
                        raw_value = df[col].iloc[0]
                        if pd.notna(raw_value):
                            return float(raw_value)

                return None

            try:
                # evaluate trend block with safe comparisons
                slope_50 = safe_get_value('slope_50')
                slope_200 = safe_get_value('slope_200')
                ud_ratio = safe_get_value('UDRatio')
                ma_150 = safe_get_value('150')
                ma_200 = safe_get_value('200')
                ma_50 = safe_get_value('50')
                volume_m = safe_get_value('$VolumeM')
                rs_rating = safe_get_value('RS_Rating') if 'RS_Rating' in df.columns else None

                trend_ok = (
                    slope_50 is not None and slope_50 > 0
                    and slope_200 is not None and slope_200 > 0
                    and ud_ratio is not None and ud_ratio > 0.75
                    and ma_150 is not None and ma_200 is not None and ma_150 > ma_200
                    and ma_50 is not None and ma_150 is not None and ma_50 > ma_150
                    and ma_50 is not None and ma_200 is not None and ma_50 > ma_200
                )

                # MOMO boolean shortcut
                momo_ok = bool(row['MOMO'])

                if (
                    volume_m is not None and volume_m >= 25
                    and rs_rating is not None and rs_rating > 80
                    and (trend_ok or momo_ok)
                ):
                    # print only if inclusion happened via MOMO (trend failed)
                    if momo_ok and not trend_ok:
                        print(f"{xyz} INCLUDED via MOMO")

                    if not df.empty:
                        # Use safe column access for TTM (preserve boolean value)
                        def safe_get_ttm_value():
                            if df is None or df.empty:
                                return None

                            # Try direct column access first
                            if 'TTM' in df.columns:
                                raw_value = df['TTM'].iloc[0]
                                if pd.notna(raw_value):
                                    return raw_value  # Keep original type (boolean)

                            # Try multi-level column access
                            for col in df.columns:
                                if isinstance(col, tuple) and len(col) >= 1 and col[0] == 'TTM':
                                    raw_value = df[col].iloc[0]
                                    if pd.notna(raw_value):
                                        return raw_value  # Keep original type (boolean)

                            return None

                        ttm_value = safe_get_ttm_value()
                        if ttm_value is not None:
                            try:
                                screenerdf.at[row.name, 'TTM'] = ttm_value
                            except (KeyError, IndexError):
                                print(f"Issue adding TTM for {xyz}")

                        # Use safe column access for RS_Rating
                        rs_value = safe_get_value('RS_Rating')
                        if rs_value is not None:
                            try:
                                screenerdf.at[row.name, 'Percentile'] = rs_value
                            except (KeyError, IndexError):
                                print(f"Issue adding RS_Rating for {xyz}")

                    # Calculate signal score for included stocks (using V3 momentum + earnings acceleration)
                    signal_score = calculate_signal_score_v3(row, df)
                    signal_strength = get_signal_strength(signal_score)

                    screenerdf.at[row.name, 'Signal_Score'] = signal_score
                    screenerdf.at[row.name, 'Signal'] = signal_strength

                    print(f"{xyz}: Signal Score = {signal_score:.1f} ({signal_strength})")
                else:
                    reason = determine_exclusion_reasons(df)
                    excluded_tickers.append((xyz, reason))
            except Exception as e:
                print(f"Warning: Error in trend analysis for {xyz}: {e}")
                reason = determine_exclusion_reasons(df)
                excluded_tickers.append((xyz, reason))
        else:
            reason = determine_exclusion_reasons(df)
            excluded_tickers.append((xyz, reason))

    return screenerdf, excluded_tickers
