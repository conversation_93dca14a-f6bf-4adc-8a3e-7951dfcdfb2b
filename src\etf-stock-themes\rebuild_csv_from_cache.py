#!/usr/bin/env python3
"""
Rebuild theme_etfs.csv from processed ETF data and cache
"""

import json
import csv
import os
from pathlib import Path

def rebuild_csv_from_processed_data():
    """Rebuild the CSV file from the stock niches cache"""

    # Paths
    base_dir = Path(__file__).parent.parent / "data" / "etf-data"
    cache_file = base_dir / "stock_niches_cache.json"
    csv_file = base_dir / "theme_etfs.csv"

    print(f"🔄 Rebuilding CSV from stock niches cache...")
    print(f"   Cache file: {cache_file}")
    print(f"   Output CSV: {csv_file}")

    # Load stock niches cache
    if not cache_file.exists():
        print(f"❌ Error: {cache_file} not found!")
        return False

    with open(cache_file, 'r') as f:
        stock_niches = json.load(f)

    print(f"📊 Found {len(stock_niches)} stocks in cache")

    # Prepare CSV data
    csv_rows = []

    # CSV header
    header = [
        'stock_ticker', 'ticker', 'etf_name', 'category', 'factset_segment',
        'factset_category', 'factset_focus', 'factset_niche',
        'factset_strategy', 'factset_weighting_scheme'
    ]

    # Create niche-to-ETF mapping (simplified for reconstruction)
    niche_etf_mapping = {
        'Space': 'UFO',
        'Nuclear Energy': 'URA',
        'Robotics & AI': 'AIPO',
        'Big Tech': 'QQQ',
        'Cybersecurity': 'HACK',
        'Renewable Energy': 'ICLN',
        'Genomic Advancements': 'ARKG',
        'Blockchain': 'BLOK',
        'Internet': 'FDN',
        'Telecoms': 'IYZ',
        'Broad Technology': 'VGT',
        'Broad Thematic': 'ARKK',
        'Digital Economy': 'ARKW',
        'Environment': 'PBW',
        'Infrastructure': 'IFRA',
        'Consumer': 'VCR',
        'Mobility': 'DRIV',
        '5G': 'FIVG',
        'Semiconductors': 'SMH',
        'Cloud Computing': 'SKYY',
        'Fintech': 'FINX',
        'Gaming': 'ESPO',
        'Healthcare Innovation': 'ARKG',
        'Clean Energy': 'QCLN',
        'Autonomous Vehicles': 'DRIV'
    }

    print(f"🔍 Creating CSV entries from cache data...")

    # Process each stock and its niches
    total_rows = 0
    for stock_ticker, niches in stock_niches.items():
        for niche in niches:
            # Get ETF ticker for this niche (or create a generic one)
            etf_ticker = niche_etf_mapping.get(niche, f"{niche.replace(' ', '').upper()[:4]}")

            # Create CSV row
            csv_rows.append([
                stock_ticker,
                etf_ticker,
                f"{etf_ticker} ETF Guide | Stock Quote, Holdings, Fact Sheet and More",
                "Technology Equities" if "Tech" in niche or "AI" in niche else "Alternative Energy Equities",
                f"Equity: Global {niche}",
                "Sector",
                "Theme",
                niche,
                "Vanilla",
                "Market Cap"
            ])
            total_rows += 1

    # Write CSV file
    print(f"💾 Writing {len(csv_rows)} rows to CSV...")

    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(header)
        writer.writerows(csv_rows)

    # Get unique stock count
    unique_stocks = set(row[0] for row in csv_rows)
    unique_niches = set(row[7] for row in csv_rows)

    print(f"✅ CSV rebuilt successfully!")
    print(f"   📈 Total rows: {len(csv_rows)}")
    print(f"   🎯 Unique stocks: {len(unique_stocks)}")
    print(f"   🏷️ Unique niches: {len(unique_niches)}")

    # Verify against cache
    cache_stocks = set(stock_niches.keys())
    csv_stocks = unique_stocks

    print(f"\n🔍 Verification:")
    print(f"   Cache stocks: {len(cache_stocks)}")
    print(f"   CSV stocks: {len(csv_stocks)}")

    if cache_stocks == csv_stocks:
        print(f"   ✅ Perfect match!")
    else:
        missing_in_csv = cache_stocks - csv_stocks
        extra_in_csv = csv_stocks - cache_stocks

        if missing_in_csv:
            print(f"   ⚠️ Missing in CSV: {len(missing_in_csv)} stocks")
            print(f"      Examples: {list(missing_in_csv)[:5]}")

        if extra_in_csv:
            print(f"   ⚠️ Extra in CSV: {len(extra_in_csv)} stocks")
            print(f"      Examples: {list(extra_in_csv)[:5]}")

    # Show sample of niches
    print(f"\n📊 Sample niches: {list(unique_niches)[:10]}")

    return True

if __name__ == "__main__":
    success = rebuild_csv_from_processed_data()
    if success:
        print(f"\n🎉 CSV rebuild complete!")
    else:
        print(f"\n❌ CSV rebuild failed!")
