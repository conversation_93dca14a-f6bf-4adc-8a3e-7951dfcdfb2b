#!/usr/bin/env python3
"""
Stock Niche Query Tool - Simple interface for O(1) stock → niches queries
"""

import sys
import os
import argparse

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.append(src_dir)

from enhanced_theme_etf_collector import EnhancedThemeETFCollector

def main():
    parser = argparse.ArgumentParser(description='Query stock niches with O(1) performance')
    parser.add_argument('stock', nargs='?', help='Stock ticker to query')
    parser.add_argument('--rebuild', action='store_true', help='Rebuild cache from CSV')
    parser.add_argument('--stats', action='store_true', help='Show cache statistics')
    parser.add_argument('--list-all', action='store_true', help='List all stocks and their niches')
    parser.add_argument('--find-niche', type=str, help='Find all stocks with specific niche')
    
    args = parser.parse_args()
    
    # Initialize collector
    collector = EnhancedThemeETFCollector()
    
    # Handle rebuild request
    if args.rebuild:
        print("🔄 Rebuilding stock-niches cache...")
        collector.rebuild_stock_niches_cache()
        return
    
    # Handle stats request
    if args.stats:
        collector.print_cache_stats()
        return
    
    # Handle list all request
    if args.list_all:
        print("📋 All Stocks and Their Niches:")
        print("=" * 50)
        for stock, niches in sorted(collector.stock_niches_cache.items()):
            print(f"{stock}: {', '.join(niches)}")
        return
    
    # Handle find niche request
    if args.find_niche:
        target_niche = args.find_niche
        matching_stocks = []
        for stock, niches in collector.stock_niches_cache.items():
            if target_niche in niches:
                matching_stocks.append(stock)
        
        print(f"🔍 Stocks with '{target_niche}' niche:")
        if matching_stocks:
            for stock in sorted(matching_stocks):
                print(f"   {stock}")
        else:
            print(f"   No stocks found with '{target_niche}' niche")
        return
    
    # Handle stock query
    if args.stock:
        stock = args.stock.upper()
        niches = collector.get_stock_niches(stock)
        
        if niches:
            print(f"🎯 {stock} niches: {', '.join(niches)}")
        else:
            print(f"❌ No niches found for {stock}")
        return
    
    # Interactive mode if no arguments
    print("🎯 Stock Niche Query Tool")
    print("=" * 40)
    collector.print_cache_stats()
    print("\nEnter stock tickers (or 'quit' to exit):")
    
    while True:
        try:
            stock = input("\nStock ticker: ").strip().upper()
            
            if stock in ['QUIT', 'EXIT', 'Q']:
                break
            
            if not stock:
                continue
            
            niches = collector.get_stock_niches(stock)
            
            if niches:
                print(f"✅ {stock}: {', '.join(niches)}")
            else:
                print(f"❌ No niches found for {stock}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except EOFError:
            break

if __name__ == "__main__":
    main()
