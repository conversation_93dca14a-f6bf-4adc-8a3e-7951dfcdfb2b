"""
Example usage of the new batched getStockDataV3 functions.
This script demonstrates how to efficiently download data for multiple tickers.
"""

import asyncio
import aiohttp
import sys
import os
import time
from datetime import datetime

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from stockdata.data_source import getStockDataV3, getStockDataV3Batch, getStockDataV3BatchChunked
from common.http_session import get_session

async def example_single_ticker():
    """Example of single ticker download (existing method)."""
    print("=== Single Ticker Example ===")

    async with aiohttp.ClientSession() as session:
        ticker = "AAPL"
        start_time = time.time()

        df = await getStockDataV3(session, ticker)

        end_time = time.time()

        if df is not None and not df.empty:
            print(f"✅ Successfully downloaded {ticker} in {end_time - start_time:.2f} seconds")
            print(f"   Shape: {df.shape}")
            print(f"   Columns: {len(df.columns)} indicators")
            print(f"   Date range: {df.index.min()} to {df.index.max()}")
        else:
            print(f"❌ Failed to download {ticker}")


async def example_small_batch():
    """Example of small batch download."""
    print("\n=== Small Batch Example (5 tickers) ===")

    async with aiohttp.ClientSession() as session:
        tickers = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA"]
        start_time = time.time()

        results = await getStockDataV3Batch(session, tickers)

        end_time = time.time()

        print(f"✅ Batch download completed in {end_time - start_time:.2f} seconds")
        print(f"   Successfully downloaded: {len(results)}/{len(tickers)} tickers")

        for ticker, df in results.items():
            if df is not None and not df.empty:
                print(f"   {ticker}: {df.shape[0]} rows, {df.shape[1]} columns")


async def example_large_batch():
    """Example of large batch with chunking."""
    print("\n=== Large Batch Example (20 tickers with chunking) ===")

    # Sample of popular tickers
    tickers = [
        "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "AMD", "CRM",
        "ORCL", "ADBE", "PYPL", "INTC", "CSCO", "AVGO", "TXN", "QCOM", "AMAT", "MU"
    ]

    async with aiohttp.ClientSession() as session:
        start_time = time.time()

        # Use chunked processing with batch size of 8
        results = await getStockDataV3BatchChunked(session, tickers, batch_size=8)

        end_time = time.time()

        print(f"✅ Chunked batch download completed in {end_time - start_time:.2f} seconds")
        print(f"   Successfully downloaded: {len(results)}/{len(tickers)} tickers")

        # Show summary statistics
        total_rows = sum(df.shape[0] for df in results.values() if df is not None)
        total_columns = sum(df.shape[1] for df in results.values() if df is not None)

        print(f"   Total data points: {total_rows} rows across all tickers")
        print(f"   Average indicators per ticker: {total_columns // len(results) if results else 0}")


async def performance_comparison():
    """Compare performance between single and batch downloads."""
    print("\n=== Performance Comparison ===")

    test_tickers = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]

    async with aiohttp.ClientSession() as session:
        # Single ticker downloads
        print("Testing individual downloads...")
        start_time = time.time()
        single_results = {}

        for ticker in test_tickers:
            df = await getStockDataV3(session, ticker)
            if df is not None:
                single_results[ticker] = df

        single_time = time.time() - start_time

        # Batch download
        print("Testing batch download...")
        start_time = time.time()

        batch_results = await getStockDataV3Batch(session, test_tickers)

        batch_time = time.time() - start_time

        # Results
        print(f"\n📊 Performance Results:")
        print(f"   Individual downloads: {single_time:.2f} seconds ({len(single_results)} tickers)")
        print(f"   Batch download: {batch_time:.2f} seconds ({len(batch_results)} tickers)")

        if batch_time > 0:
            speedup = single_time / batch_time
            print(f"   Speedup: {speedup:.1f}x faster with batch processing")


async def main():
    """Run all examples."""
    print(f"🚀 Batch getStockDataV3 Examples - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        await example_single_ticker()
        await example_small_batch()
        await example_large_batch()
        await performance_comparison()

        print("\n" + "=" * 60)
        print("✅ All examples completed successfully!")

    except Exception as e:
        print(f"\n❌ Error occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())