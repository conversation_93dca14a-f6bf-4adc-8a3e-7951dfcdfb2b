#!/usr/bin/env python3
"""
Simple ETF themes scraper using pyetfdb-scraper library
Ready to integrate into your existing scraper
"""

import time
from pyetfdb_scraper.etf import ETF

def get_etf_themes(ticker):
    """
    Get ETF themes using pyetfdb-scraper library
    
    Args:
        ticker (str): ETF ticker symbol
        
    Returns:
        list: List of theme strings for the ETF
    """
    
    try:
        # Create ETF object
        etf = ETF(ticker)
        
        # Extract themes from multiple sources
        themes = set()
        
        if hasattr(etf, 'info') and isinstance(etf.info, dict):
            # Get themes from dbtheme (ETF Database themes)
            dbtheme = etf.info.get('dbtheme', {})
            if dbtheme:
                category = dbtheme.get('category', '')
                asset_class = dbtheme.get('asset_class', '')
                asset_class_size = dbtheme.get('asset_class_size', '')
                asset_class_style = dbtheme.get('asset_class_style', '')
                general_region = dbtheme.get('general_region', '')
                specific_region = dbtheme.get('specific_region', '')
                
                for theme in [category, asset_class, asset_class_size, asset_class_style, general_region, specific_region]:
                    if theme and theme.strip():
                        themes.add(theme.strip())
            
            # Get themes from fact_set (FactSet classifications)
            fact_set = etf.info.get('fact_set', {})
            if fact_set:
                for key in ['segment', 'category', 'focus', 'niche', 'strategy', 'weighting_scheme']:
                    values = fact_set.get(key, [])
                    if isinstance(values, list):
                        for value in values:
                            if value and value.strip():
                                themes.add(value.strip())
        
        # Convert to sorted list
        return sorted(list(themes))
        
    except Exception as e:
        print(f"Error extracting themes for {ticker}: {e}")
        return []

def test_etf_themes():
    """Test the ETF themes extraction"""
    
    print("Testing ETF themes extraction...")
    print("=" * 50)
    
    test_tickers = ["SPY", "QQQ", "VTI", "IWM", "ARKK"]
    
    for ticker in test_tickers:
        print(f"\n{ticker}:")
        start_time = time.time()
        themes = get_etf_themes(ticker)
        end_time = time.time()
        
        if themes:
            print(f"  ✅ Found {len(themes)} themes ({end_time - start_time:.2f}s)")
            for theme in themes:
                print(f"    - {theme}")
        else:
            print(f"  ❌ No themes found ({end_time - start_time:.2f}s)")

if __name__ == "__main__":
    test_etf_themes()
