// Lazy Loading with Smart Caching
class ImageCache {
  constructor(maxSize = 500) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.accessOrder = [];
  }

  get(key) {
    if (this.cache.has(key)) {
      // Move to end (most recently used)
      this.accessOrder = this.accessOrder.filter(k => k !== key);
      this.accessOrder.push(key);
      return this.cache.get(key);
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.set(key, value);
      return;
    }

    // Evict oldest if at capacity
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.accessOrder.shift();
      const oldValue = this.cache.get(oldestKey);
      if (oldValue && typeof oldValue === 'string' && oldValue.startsWith('blob:')) {
        URL.revokeObjectURL(oldValue);
      }
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, value);
    this.accessOrder.push(key);
  }

  clear() {
    // Clean up blob URLs
    for (const [key, value] of this.cache) {
      if (typeof value === 'string' && value.startsWith('blob:')) {
        URL.revokeObjectURL(value);
      }
    }
    this.cache.clear();
    this.accessOrder = [];
  }
}

const imageCache = new ImageCache(500);
const loadingImages = new Set();

async function loadLazyImage(img) {
  const ticker = img.dataset.ticker;
  const type = img.dataset.type;
  const cacheKey = `${ticker}_${type}`;

  // Check cache first
  const cachedUrl = imageCache.get(cacheKey);
  if (cachedUrl) {
    img.src = cachedUrl;
    img.classList.add('loaded');
    img.classList.remove('loading');
    return;
  }

  // Prevent duplicate requests
  if (loadingImages.has(cacheKey)) {
    return;
  }

  loadingImages.add(cacheKey);
  img.classList.add('loading');

  try {
    const response = await fetch(`/img?t=${encodeURIComponent(ticker)}&type=${type}`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const blob = await response.blob();
    const imageUrl = URL.createObjectURL(blob);

    // Cache the blob URL
    imageCache.set(cacheKey, imageUrl);

    // Update image
    img.src = imageUrl;
    img.classList.add('loaded');
    img.classList.remove('loading');

  } catch (error) {
    console.error(`Failed to load image for ${ticker} (${type}):`, error);
    // Set error placeholder
    img.src = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='200'%3E%3Crect width='100%25' height='100%25' fill='%23fee2e2'/%3E%3Ctext x='50%25' y='50%25' font-family='system-ui' font-size='14' fill='%23dc2626' text-anchor='middle' dy='0.3em'%3EFailed to load chart%3C/text%3E%3C/svg%3E`;
    img.classList.remove('loading');
  } finally {
    loadingImages.delete(cacheKey);
  }
}

// Intersection Observer for lazy loading
const imageObserver = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target;
      loadLazyImage(img);
      imageObserver.unobserve(img);
    }
  });
}, {
  rootMargin: '100px' // Start loading 100px before image is visible
});

// Initialize lazy loading
function initializeLazyLoading() {
  const lazyImages = document.querySelectorAll('.lazy-image');
  lazyImages.forEach(img => {
    imageObserver.observe(img);
  });
}

// Preload images for current card in one-per-page mode
function preloadCurrentCardImages() {
  if (!document.body.classList.contains('card-only')) return;

  const cards = document.querySelectorAll('.card');
  const currentCard = cards[currentIdx];
  if (currentCard) {
    const images = currentCard.querySelectorAll('.lazy-image');
    images.forEach(img => {
      if (!img.src.includes('blob:') && !img.src.includes('/img?')) {
        loadLazyImage(img);
      }
    });
  }
}

function updateFilter(param, value) {
  const url = new URL(window.location);
  if (value) {
    url.searchParams.set(param, value);
  } else {
    url.searchParams.delete(param);
  }

  // Clear dependent filters when parent changes
  if (param === 'sector') {
    url.searchParams.delete('industry');
    url.searchParams.delete('sub_industry');
  } else if (param === 'industry') {
    url.searchParams.delete('sub_industry');
  }

  // Clear cache on filter change since data set changes
  imageCache.clear();

  window.location.href = url.toString();
}

async function like(t, btn) {
  await fetch('http://127.0.0.1:5001/like?t=' + encodeURIComponent(t));
  btn.textContent = '✓ Liked';
}
async function dislike(t, btn) {
    await fetch('http://127.0.0.1:5001/dislike?t=' + encodeURIComponent(t));
    const likeBtn = btn.previousElementSibling;
    likeBtn.textContent = 'Like';
}
async function saveWeek() {
  const btns = document.querySelectorAll('header button');
  const saveBtn = btns[0];
  saveBtn.disabled = true;
  const resp = await fetch('/save_week');
  const text = await resp.text();
  alert(text);
  saveBtn.disabled = false;
}
let currentIdx = 0;
function applyCardOnly() {
  const cards = Array.from(document.querySelectorAll('.card'));
  if (!document.body.classList.contains('card-only')) {
    cards.forEach(c => c.style.display = '');
    return;
  }
  cards.forEach((c,i)=> { c.style.display = i === currentIdx ? '' : 'none'; });
  cards[currentIdx]?.scrollIntoView({behavior:'instant', block:'start'});

  // Preload images for current card
  setTimeout(preloadCurrentCardImages, 100);
}
function toggleCardOnly(on) {
  document.body.classList.toggle('card-only', !!on);
  applyCardOnly();
  document.querySelector('.controls')?.scrollIntoView({behavior:'instant'});
}
function prevCard() {
  const total = document.querySelectorAll('.card').length;
  if (total === 0) return;
  currentIdx = (currentIdx - 1 + total) % total;
  applyCardOnly();
}
function nextCard() {
  const total = document.querySelectorAll('.card').length;
  if (total === 0) return;
  currentIdx = (currentIdx + 1) % total;
  applyCardOnly();
}
(function initToggles(){
  if (location.hash.includes('card')) {
    document.getElementById('cardOnlyToggle').checked = true;
    document.body.classList.add('card-only');
    applyCardOnly();
  }
})();
// (function enforceOnePerPage(){
//   document.body.classList.add('card-only');
//   const cb = document.getElementById('cardOnlyToggle');
//   if (cb) cb.checked = true;
//   applyCardOnly();
// })();
addEventListener('resize', () => applyCardOnly());

// Initialize lazy loading after DOM is ready
document.addEventListener('DOMContentLoaded', initializeLazyLoading);
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeLazyLoading);
} else {
  initializeLazyLoading();
}

// Refresh Chart IDs function
async function refreshChartIds() {
  const button = event.target;
  const originalText = button.textContent;

  try {
    button.textContent = '🔄 Refreshing...';
    button.disabled = true;

    const response = await fetch('/refresh-chart-ids');
    const result = await response.json();

    if (result.success) {
      // Clear image cache to force reload with new IDs
      imageCache.clear();

      // Reload all chart images
      const lazyImages = document.querySelectorAll('.lazy-image');
      lazyImages.forEach(img => {
        if (img.dataset.loaded === 'true') {
          img.dataset.loaded = 'false';
          img.src = img.dataset.placeholder || img.src;
        }
      });

      // Re-trigger lazy loading
      initializeLazyLoading();

      button.textContent = '✅ Refreshed';
      setTimeout(() => {
        button.textContent = originalText;
      }, 2000);

      console.log('Chart IDs refreshed:', result);
    } else {
      throw new Error(result.message || 'Failed to refresh chart IDs');
    }
  } catch (error) {
    console.error('Error refreshing chart IDs:', error);
    button.textContent = '❌ Failed';
    setTimeout(() => {
      button.textContent = originalText;
    }, 2000);
  } finally {
    button.disabled = false;
  }
}

// ETF Analytics functionality
function showETFAnalytics() {
  window.open('/etf-analytics-page', '_blank');
}

// Clean up blob URLs when page unloads
window.addEventListener('beforeunload', () => {
  imageCache.clear();
});
