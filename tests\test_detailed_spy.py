#!/usr/bin/env python3
"""
Test script to get detailed SPY data and see all available content
"""

import time
from pyetfdb_scraper.etf import ETF
import json

def explore_spy_data():
    """Get all available data for SPY to see what's available"""
    
    print("Exploring detailed SPY data...")
    print("=" * 60)
    
    try:
        etf = ETF("SPY")
        
        # Print the full analyst report
        if hasattr(etf, 'info') and isinstance(etf.info, dict):
            analyst_report = etf.info.get('analyst_report', '')
            print(f"Full Analyst Report:")
            print(f"'{analyst_report}'")
            print(f"Length: {len(analyst_report)} characters")
        
        # Check if there's more detailed content in other fields
        print(f"\n--- All Info Fields ---")
        if hasattr(etf, 'info') and isinstance(etf.info, dict):
            for key, value in etf.info.items():
                print(f"\n{key}:")
                if isinstance(value, str):
                    print(f"  '{value}'")
                elif isinstance(value, dict):
                    print(f"  {json.dumps(value, indent=2)}")
                elif isinstance(value, list):
                    print(f"  {value}")
                else:
                    print(f"  {value}")
        
        # Check base_info
        print(f"\n--- Base Info ---")
        if hasattr(etf, 'base_info'):
            print(json.dumps(etf.base_info, indent=2))
        
        # Check if there's a to_dict method that gives us everything
        print(f"\n--- Full ETF Data (to_dict) ---")
        if hasattr(etf, 'to_dict'):
            try:
                full_data = etf.to_dict()
                print(f"Keys in to_dict(): {list(full_data.keys())}")
                
                # Look for any field that might contain the detailed description
                for key, value in full_data.items():
                    if isinstance(value, str) and len(value) > 200:
                        print(f"\n{key} (long text):")
                        print(f"'{value}'")
                    elif isinstance(value, dict):
                        # Check if any dict values contain long text
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, str) and len(sub_value) > 200:
                                print(f"\n{key}.{sub_key} (long text):")
                                print(f"'{sub_value}'")
                                
            except Exception as e:
                print(f"Error calling to_dict(): {e}")
        
        # Check the raw HTML soup for any content we might be missing
        print(f"\n--- Checking Raw HTML ---")
        if hasattr(etf, 'etf_ticker_body_soup'):
            soup = etf.etf_ticker_body_soup
            
            # Look for text that contains the specific content you mentioned
            page_text = soup.get_text()
            if 'largest and most heavily-traded ETFs' in page_text:
                print("✅ Found the detailed text in HTML!")
                
                # Find the specific paragraph
                for element in soup.find_all(['p', 'div', 'section']):
                    text = element.get_text(strip=True)
                    if 'largest and most heavily-traded ETFs' in text:
                        print(f"Found in element: {element.name}")
                        print(f"Full text: '{text}'")
                        break
            else:
                print("❌ Detailed text not found in HTML")
                
                # Look for any substantial paragraphs
                print("Looking for substantial paragraphs...")
                for element in soup.find_all('p'):
                    text = element.get_text(strip=True)
                    if len(text) > 200 and 'SPY' in text:
                        print(f"Found paragraph about SPY: '{text}'")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    explore_spy_data()
