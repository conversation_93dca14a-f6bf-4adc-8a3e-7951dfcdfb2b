#!/usr/bin/env python3
"""
Test script for ETF theme integration in enhanced_analysis.py
"""

import sys
import os

# Add src to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(src_dir)

from enhanced_analysis import check_and_update_etf_themes

def test_etf_integration():
    print("🧪 Testing ETF Theme Integration in Enhanced Analysis")
    print("=" * 60)
    
    # Test with a mix of existing and new stocks
    test_stocks = [
        'TSLA',    # Should exist
        'AAPL',    # Should exist  
        'OKLO',    # Should exist
        'NVDA',    # Should exist
        'FAKE123', # Should not exist (fake ticker)
        'XYZ456',  # Should not exist (fake ticker)
    ]
    
    print(f"\n📊 Testing with stocks: {test_stocks}")
    
    # Test the integration function
    print(f"\n🔍 Running ETF theme check...")
    results = check_and_update_etf_themes(test_stocks)
    
    print(f"\n📈 Results:")
    for key, value in results.items():
        print(f"   {key}: {value}")
    
    print(f"\n✅ ETF integration test completed!")

if __name__ == "__main__":
    test_etf_integration()
